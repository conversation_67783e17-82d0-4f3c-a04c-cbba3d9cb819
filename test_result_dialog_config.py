#!/usr/bin/env python3
"""
測試結果對話框配置功能
驗證AutoScoringResultDialog是否正確使用settings.yaml中的result_display設定
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'code'))

from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from gui_main import AutoScoringResultDialog
import yaml

class TestMainWindow(QMainWindow):
    """測試主視窗"""
    
    def __init__(self):
        super().__init__()
        self.load_config()
        self.init_ui()
        
    def load_config(self):
        """載入配置"""
        try:
            with open('settings.yaml', 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        except FileNotFoundError:
            print("Warning: settings.yaml not found, using default config")
            self.config = {
                'gui': {
                    'result_display': {
                        'show_duration': 15,
                        'font_size': 24,
                        'window_size': [500, 450]
                    }
                }
            }
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("Test Result Dialog Configuration")
        self.setGeometry(100, 100, 600, 400)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 顯示當前配置
        result_config = self.config.get('gui', {}).get('result_display', {})
        show_duration = result_config.get('show_duration', 15)
        font_size = result_config.get('font_size', 24)
        window_size = result_config.get('window_size', [500, 450])
        
        print(f"Current result_display configuration:")
        print(f"  show_duration: {show_duration} seconds")
        print(f"  font_size: {font_size}")
        print(f"  window_size: {window_size}")
        
        # 測試按鈕
        test_button = QPushButton("Test Result Dialog")
        test_button.clicked.connect(self.show_test_dialog)
        layout.addWidget(test_button)
        
        # 不同配置測試按鈕
        test_small_button = QPushButton("Test Small Dialog (font_size=16, window=[300,250])")
        test_small_button.clicked.connect(self.show_small_dialog)
        layout.addWidget(test_small_button)
        
        test_large_button = QPushButton("Test Large Dialog (font_size=32, window=[600,500])")
        test_large_button.clicked.connect(self.show_large_dialog)
        layout.addWidget(test_large_button)
        
        test_fast_button = QPushButton("Test Fast Close (show_duration=3)")
        test_fast_button.clicked.connect(self.show_fast_dialog)
        layout.addWidget(test_fast_button)
    
    def create_test_result(self):
        """創建測試結果數據"""
        return {
            'final_score': 78.5,
            'avg_angle_score': 85.2,
            'avg_speed_score': 72.8,
            'avg_shoulder_balance_score': 88.1,
            'avg_hunchback_score': 68.9,
            'level': 'good',
            'session_duration': 8.5,
            'sample_count': 255
        }
    
    def show_test_dialog(self):
        """顯示使用當前配置的測試對話框"""
        result = self.create_test_result()
        dialog = AutoScoringResultDialog(result, self, self.config)
        dialog.exec()
    
    def show_small_dialog(self):
        """顯示小尺寸對話框"""
        result = self.create_test_result()
        test_config = {
            'gui': {
                'result_display': {
                    'show_duration': 10,
                    'font_size': 16,
                    'window_size': [300, 250]
                }
            }
        }
        dialog = AutoScoringResultDialog(result, self, test_config)
        dialog.exec()
    
    def show_large_dialog(self):
        """顯示大尺寸對話框"""
        result = self.create_test_result()
        test_config = {
            'gui': {
                'result_display': {
                    'show_duration': 8,
                    'font_size': 32,
                    'window_size': [600, 500]
                }
            }
        }
        dialog = AutoScoringResultDialog(result, self, test_config)
        dialog.exec()
    
    def show_fast_dialog(self):
        """顯示快速關閉對話框"""
        result = self.create_test_result()
        test_config = {
            'gui': {
                'result_display': {
                    'show_duration': 3,
                    'font_size': 24,
                    'window_size': [400, 350]
                }
            }
        }
        dialog = AutoScoringResultDialog(result, self, test_config)
        dialog.exec()

def main():
    """主函數"""
    app = QApplication(sys.argv)
    app.setApplicationName("Result Dialog Configuration Test")
    
    window = TestMainWindow()
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
