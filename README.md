# Walking Gait Detection and Speed Calculation System

## Project Overview

This project is based on MediaPipe Pose, implementing real-time human skeleton detection, key joint angle calculation, walking gait assessment, and walking speed calculation. It features a modern PySide6 GUI interface with separate progress bars for angle and speed metrics.
Suitable for sports science, rehabilitation monitoring, smart health, and other scenarios.

---

## Key Features

1. **Real-time Skeleton Detection**: Automatically captures human skeleton key points from camera or video.
2. **Key Joint Angle Calculation**: Calculates angles of hip, knee, ankle and other key joints with real-time display.
3. **Three-level Gait Assessment**: Categorizes gait quality into Excellent, Good, and Poor levels based on angle and speed conditions.
4. **Walking Speed Calculation**: Calculates walking speed based on movement distance and time of human key points. Displays in m/s when pixel_to_meter is calibrated, or pixels/s for relative measurements.
5. **Modern PySide6 GUI Interface**: Professional graphical user interface with organized layout and modern styling.
6. **Separate Progress Bars**: Individual gradient progress bars for "Joint Angle Quality" and "Walking Speed Quality" with red-yellow-green color scheme and descriptive tooltips.
7. **Real-time Camera Feed**: Live video display with pose landmark overlay within the GUI, featuring compact layout for optimal space utilization.
8. **Configurable Parameters**: Adjust angle and speed thresholds through settings.yaml file for easy parameter tuning.
9. **No Person Detection**: Displays "No Person Detected" when no one is in the frame, with all metrics showing 0.
10. **Multi-person Support**: When expanded for multi-person detection, displays only the largest (closest to camera) person.
11. **Auto Scoring System**: Automatic gait evaluation with session-based scoring when a person enters and leaves the camera view.
12. **Smart Session Detection**: Automatically detects when a person enters the camera frame and starts collecting gait data for evaluation.
13. **Comprehensive Scoring Results**: Displays final scores with detailed breakdown including average angle score, average speed score, and overall assessment level.
14. **Timed Result Display**: Shows scoring results in a large, clear dialog with 15-second auto-close countdown for convenient viewing.
15. **Demo Mode**: Advanced demonstration mode with realistic data simulation for each indicator, perfect for presentations and testing without camera input. Each indicator can be independently controlled with customizable value ranges, change speeds, and fluctuation patterns.

---

## Installation and Execution

### 1. System Requirements

- Python 3.7+
- OpenCV
- MediaPipe
- NumPy
- PySide6 (for GUI)
- PyYAML (for configuration)

### 2. Installation Steps

```bash
cd walking_tester/code
pip install -r requirements.txt
```

**Note**: The new version requires PyYAML for configuration file reading and PySide6 for the GUI interface.

### 3. Execution Methods

#### GUI Version (Recommended)
```bash
python gui_main.py
```

#### Simple GUI Test
```bash
python gui_simple.py
```

#### Command Line Version (Legacy)
```bash
python main.py
```

#### Dependency Check
```bash
python gui_launcher.py
```

---

## Auto Scoring Feature

### Overview
The Auto Scoring system provides automatic gait evaluation when a person enters and leaves the camera view. This feature is designed for convenient, hands-free assessment of walking patterns.

### How to Use

1. **Enable Auto Scoring**: In the GUI control panel, check the "Enable Auto Scoring" checkbox.
2. **Start Camera**: Click on the camera feed area to start the camera.
3. **Walk into View**: When a person enters the camera frame, the system will automatically start collecting gait data.
4. **Walk out of View**: When the person leaves the camera frame, the system will calculate and display the final score.
5. **View Results**: A result dialog will appear showing the comprehensive scoring breakdown with a 15-second auto-close timer.

### Scoring Methodology

- **Average Angle Score**: Based on knee joint angles during the walking session
- **Average Speed Score**: Based on walking speed consistency and quality
- **Final Score**: Weighted combination of angle and speed scores (default: 60% angle, 40% speed)
- **Assessment Levels**:
  - **Excellent**: Score ≥ 80 (Green)
  - **Good**: Score ≥ 60 (Orange)
  - **Poor**: Score < 60 (Red)

### Configuration

Auto scoring parameters can be adjusted in `settings.yaml`:

```yaml
auto_scoring:
  enabled: false                    # Default state
  detection:
    enter_threshold: 5              # Frames to confirm person entered
    exit_threshold: 10              # Frames to confirm person left
    min_session_duration: 3.0       # Minimum session duration (seconds)
  calculation:
    min_samples: 5                  # Minimum samples for valid scoring
  result_display:
    show_duration: 15               # Result display duration (seconds)
    font_size: 24                   # Result dialog font size

# Scoring weights are configured in gait_analysis section:
gait_analysis:
  scoring:
    angle_weight: 0.3               # Weight for angle score (30%)
    speed_weight: 0.7               # Weight for speed score (70%)
```

---

## Project Architecture

```
walking_tester/
  settings.yaml            # System configuration file
  code/
    gui_main.py            # Main PySide6 GUI application
    gui_widgets.py         # Custom GUI widgets and components
    gui_simple.py          # Simple GUI test application
    gui_launcher.py        # GUI launcher with dependency check
    styles.qss             # PySide6 stylesheet
    main.py                # Legacy command-line interface
    videocapture.py        # Camera/video capture
    pose_detection.py      # MediaPipe pose detection
    angle_calculator.py    # Key joint angle calculation
    gait_analyzer.py       # Gait assessment and speed calculation
    visualizer.py          # Legacy OpenCV visualization
    requirements.txt       # Dependency list
```

---

## Module Descriptions

### 1. gui_main.py

- Main PySide6 GUI application with modern interface
- Real-time video processing in separate thread
- Integrated camera feed display with pose landmarks and compact layout
- Separate progress bars for "Joint Angle Quality" and "Walking Speed Quality" metrics with clear labeling and tooltips
- Professional styling and responsive layout with optimized spacing and dedicated sections
- Adaptive speed unit display (m/s or pixels/s based on calibration)
- Enhanced UI layout with optimized margins, spacing, and heights for clear label visibility
- Structured progress bar sections with individual frames for better organization
- Compact progress bar design that doesn't obstruct other UI elements
- Linear progress bar scoring system that accurately reflects detection status and performance
- Zero-score display when no keypoints are detected or speed is zero
- **Auto Scoring System**: Automatic session-based gait evaluation with person detection
- **Auto Scoring Control Panel**: Toggle switch for enabling/disabling auto scoring with status indicators
- **Auto Scoring Result Dialog**: Large, clear result display with countdown timer and detailed score breakdown

### 2. gui_widgets.py

- Custom GUI components including GradientProgressBar
- StatusPanel, MetricDisplay, and AngleDisplay widgets
- Animated progress bars with smooth transitions
- Modern styling with consistent color schemes

### 3. videocapture.py

- Handles camera or video frame capture
- Provides unified image source interface

### 4. pose_detection.py

- Utilizes MediaPipe Pose to obtain human skeleton key point coordinates
- Supports single-person detection

### 5. angle_calculator.py

- Calculates hip, knee, ankle and other joint angles based on skeleton key points
- Provides angle smoothing processing (optional)

### 6. gait_analyzer.py

- Implements three-level gait assessment system (Excellent, Good, Poor)
- Reads configurable angle and speed thresholds from settings.yaml
- Calculates walking speed based on hip key point movement distance and time
- Provides comprehensive scoring and smoothing functionality
  - Speed calculation recommendations:
    - Track hip movement distance in the frame
    - Combine with known camera calibration (pixels to actual distance) to convert to m/s
    - If no calibration (pixel_to_meter: null), display relative speed in pixels/s
    - GUI automatically adapts speed unit display based on calibration status

### 7. visualizer.py (Legacy)

- Overlays skeleton lines, angle values, gait assessment results and speed information on OpenCV frames
- Draws gradient progress bars showing real-time gait quality scores
- Supports three-level gait display (Excellent/Good/Poor) with color coding

### 8. settings.yaml

- Centralized system configuration parameter management
- Adjustable three-level thresholds for angles and speed
- Configure progress bar display parameters and color settings
- Support for smoothing processing and system parameter adjustment
- GUI color scheme and layout configuration

---

## 關節角度計算方式

本系統計算關節角度（如膝關節）時，採用三點夾角的向量餘弦定理：

- 假設三點分別為 A（上游點，如髖）、B（關節點，如膝）、C（下游點，如踝），計算夾角 ABC。
- 公式如下：

  1. 以 B 為頂點，計算向量 BA = A - B，向量 BC = C - B。
  2. 角度 θ = arccos( (BA · BC) / (|BA| * |BC|) )
  3. 轉換為角度（degree）：θ = degrees(θ)

- 以膝關節為例：
  - 左膝角度 = angle( left_hip, left_knee, left_ankle )
  - 右膝角度 = angle( right_hip, right_knee, right_ankle )

- 這種方法可擴展至髖、踝等其他關節。

---

## 三級步態評估系統

新版本採用三級評估系統，將步態品質分為三個等級：

### 評估標準

**角度條件（膝關節角度）：**
- **優良**：正常行走範圍 60°-100° 或 160°-180°
- **一般**：輕微異常範圍 100°-130° 或 130°-160°
- **不佳**：兩腿同時在 130°-160° 範圍內

**速度條件（雙模式支援）：**

*實際速度模式（pixel_to_meter 有設定值）：*
- **優良**：行走速度 ≥ 1.1 m/s
- **一般**：行走速度 0.8-1.1 m/s
- **不佳**：行走速度 < 0.8 m/s

*相對速度模式（pixel_to_meter 為 null）：*
- **優良**：移動速度 ≥ 50 pixels/s
- **一般**：移動速度 20-50 pixels/s
- **不佳**：移動速度 < 20 pixels/s

### 綜合評分

- 角度條件權重：60%
- 速度條件權重：40%
- 最終評分範圍：0-100
  - 70-100：優良等級（綠色）
  - 30-70：一般等級（黃色）
  - 0-30：不佳等級（紅色）

### 線性評分系統

**角度評分 (0-100分)**：
- 無檢測到關鍵點或角度為0：0分
- 優良範圍 (60-100°, 160-180°)：線性映射到80-100分
- 良好範圍 (100-130°, 130-160°)：線性映射到50-80分
- 不佳範圍 (130-160°)：線性映射到0-30分
- 其他範圍：20分基礎分

**速度評分 (0-100分)**：
- 速度為零或接近零 (≤0.01)：0分
- 優良速度 (≥1.1 m/s)：線性映射到80-100分
- 良好速度 (0.8-1.1 m/s)：線性映射到50-80分
- 低速範圍 (<0.8 m/s)：線性映射到10-50分

### 配置檔案

所有閾值參數都可在 `settings.yaml` 中調整：

```yaml
gait_analysis:
  angle_thresholds:
    excellent_ranges: [[60, 100], [160, 180]]
    good_ranges: [[100, 130], [130, 160]]
    poor_range: [130, 160]
  speed_thresholds:
    meters_per_second:
      excellent: 1.1
      good: 0.8
    pixels_per_second:
      excellent: 0.25
      good: 0.12
```

---

## Demo模式

系統提供了強大的Demo模式功能，可以在沒有攝影機輸入的情況下模擬真實的數據變化，非常適合產品展示和系統測試。

### 主要特點

1. **獨立控制**：四個指標可以獨立開啟/關閉Demo模式
2. **真實模擬**：數值會像真實數據一樣不斷跳動變化
3. **可配置參數**：完全可自訂的數值範圍、變化速度和波動幅度
4. **週期性變化**：支援基於正弦波的週期性變化模式
5. **混合模式**：可以部分指標使用Demo，部分使用真實演算法

### 快速啟用

在 `settings.yaml` 中設定：

```yaml
demo_mode:
  enabled: true                     # 啟用Demo模式
  indicators:
    angle:
      enabled: true                 # 啟用角度指標Demo
      value_range: [85, 98]         # 數值範圍
      change_speed: 1.5             # 變化速度
      fluctuation: 2                # 波動幅度
    speed:
      enabled: true                 # 啟用速度指標Demo
      value_range: [80, 95]
      change_speed: 2.0
      fluctuation: 3
```

### 使用場景

- **產品展示**：展示系統功能和界面效果
- **系統測試**：測試UI響應和數據處理
- **開發調試**：無需攝影機即可測試功能
- **培訓演示**：教學和培訓用途

詳細的Demo模式配置和使用說明請參考 `DEMO_MODE_GUIDE.md`。

---

## 行走速度計算說明

1. **關鍵點選擇**：建議以髖部（left_hip, right_hip）或腳踝（left_ankle, right_ankle）作為追蹤點。
2. **距離計算**：每幀計算該點在 X 軸（或 Y 軸，視攝影機擺放）上的像素移動距離。
3. **時間計算**：根據幀率（fps）計算時間間隔。
4. **速度公式**：  
   v = Δd / Δt
   - 若有實際標定，Δd 換算為公尺；否則僅顯示像素/秒。
5. **平滑處理**：可用移動平均法平滑速度曲線。

---

## 視覺化功能

### 漸變色進度條

- **位置**：畫面左上方，可在 settings.yaml 中調整
- **顏色**：從紅色（不佳）漸變到黃色（一般）再到綠色（優良）
- **即時更新**：只有偵測到人物時才顯示進度條
- **評分顯示**：同時顯示數值評分和等級文字

### 三級狀態顯示

- **優良步態**：綠色文字顯示
- **一般步態**：黃色文字顯示，並標示主要問題（角度/速度）
- **不佳步態**：紅色文字顯示，並標示主要問題（角度/速度）

## 開發建議

- **模組化設計**：每個功能獨立成檔，便於維護與擴展。
- **配置檔案管理**：所有參數集中在 settings.yaml，方便調整和部署。
- **日誌與錯誤處理**：關鍵步驟加上日誌輸出，便於除錯。
- **單元測試**：角度計算、速度計算等核心演算法建議撰寫測試。

---

## 後續擴展

- 支援多人體偵測
- 更多關節角度分析（髖關節、踝關節）
- 步態週期、步頻、步幅等參數計算
- 機器學習模型整合進行異常步態分類
- Web 端或行動端部署
- 數據儲存與分析報表
- 歷史評分趨勢圖表
- 個人化閾值調整建議

---

## 聯絡與貢獻

歡迎 issue、pull request 或聯絡開發者共同完善本專案！ 