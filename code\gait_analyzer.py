import numpy as np
import math
import time
import random
from collections import deque

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("Warning: PyYAML not installed, using default configuration")

class GaitAnalyzer:
    def __init__(self, config_path=None):
        # 自動檢測配置檔案路徑
        if config_path is None:
            import os
            # 嘗試不同的路徑
            possible_paths = [
                'settings.yaml',           # 當前目錄
                '../settings.yaml',        # 上一級目錄
                os.path.join(os.path.dirname(__file__), '..', 'settings.yaml')  # 相對於此檔案的上級目錄
            ]
            config_path = '../settings.yaml'  # 預設路徑
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    break
        # 載入配置檔案
        self.config = self._load_config(config_path)

        # 從配置中獲取參數
        gait_config = self.config['gait_analysis']
        self.angle_thresholds = gait_config['angle_thresholds']
        self.speed_thresholds_config = gait_config['speed_thresholds']
        self.shoulder_balance_thresholds = gait_config['shoulder_balance_thresholds']
        self.hunchback_thresholds = gait_config['hunchback_thresholds']
        self.scoring = gait_config['scoring']

        # Demo模式配置
        self.demo_config = self.config.get('demo_mode', {})
        self.demo_enabled = self.demo_config.get('enabled', False)
        self.demo_indicators = self.demo_config.get('indicators', {})
        self.demo_global_settings = self.demo_config.get('global_settings', {})

        # Demo模式狀態變量
        self.demo_values = {
            'angle': 80.0,
            'speed': 75.0,
            'shoulder_balance': 85.0,
            'hunchback': 70.0
        }
        self.demo_targets = {
            'angle': 80.0,
            'speed': 75.0,
            'shoulder_balance': 85.0,
            'hunchback': 70.0
        }
        self.last_demo_update = time.time()
        self.demo_cycle_start = time.time()

        # 平滑處理參數
        smoothing = gait_config['smoothing']
        self.speed_window = smoothing['speed_window']
        self.score_window = smoothing['score_window']

        # 系統參數
        self.pixel_to_meter = self.config['system']['pixel_to_meter']

        # 根據 pixel_to_meter 設定選擇適當的速度閾值
        self._update_speed_thresholds()

        # 初始化數據緩存
        self.prev_positions = deque(maxlen=self.speed_window)
        self.score_history = deque(maxlen=self.score_window)

    def _update_speed_thresholds(self):
        """根據 pixel_to_meter 設定選擇適當的速度閾值"""
        if self.pixel_to_meter is not None:
            # 使用實際速度閾值（公尺/秒）
            self.speed_thresholds = self.speed_thresholds_config['meters_per_second']
            self.speed_unit = "m/s"
            print(f"使用實際速度閾值: excellent={self.speed_thresholds['excellent']} m/s, good={self.speed_thresholds['good']} m/s")
        else:
            # 使用相對速度閾值（像素/秒）
            self.speed_thresholds = self.speed_thresholds_config['pixels_per_second']
            self.speed_unit = "pixels/s"
            print(f"使用相對速度閾值: excellent={self.speed_thresholds['excellent']} pixels/s, good={self.speed_thresholds['good']} pixels/s")

    def get_speed_unit(self):
        """獲取當前使用的速度單位"""
        return self.speed_unit

    def get_speed_thresholds(self):
        """獲取當前使用的速度閾值"""
        return self.speed_thresholds

    def _load_config(self, config_path):
        """Load YAML configuration file"""
        if not YAML_AVAILABLE:
            print("PyYAML not installed, using default configuration")
            return self._get_default_config()

        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            print(f"Configuration file {config_path} not found, using default values")
            return self._get_default_config()
        except Exception as e:
            print(f"Configuration file parsing error: {e}, using default values")
            return self._get_default_config()

    def _get_default_config(self):
        """Return default configuration"""
        return {
            'gait_analysis': {
                'angle_thresholds': {
                    'excellent_ranges': [[60, 100], [160, 180]],
                    'good_ranges': [[100, 130], [130, 160]],
                    'poor_range': [130, 160]
                },
                'speed_thresholds': {
                    'meters_per_second': {
                        'excellent': 1.1,
                        'good': 0.8
                    },
                    'pixels_per_second': {
                        'excellent': 0.25,
                        'good': 0.12
                    }
                },
                'shoulder_balance_thresholds': {
                    'excellent_threshold': 0.02,
                    'good_threshold': 0.05
                },
                'hunchback_thresholds': {
                    'head_neck_angle': {
                        'excellent_range': [45, 60],
                        'good_range': [35, 70],
                        'poor_min': 20,
                        'poor_max': 80
                    },
                    'cvt_angle': {
                        'excellent_min': 48,
                        'good_min': 42,
                        'poor_max': 38
                    },
                    'forward_head_posture': {
                        'excellent_max': 0.1,
                        'good_max': 0.2
                    },
                    'rounded_shoulders': {
                        'excellent_range': [80, 100],
                        'good_range': [70, 110]
                    }
                },
                'scoring': {
                    'angle_weight': 0.25,
                    'speed_weight': 0.45,
                    'shoulder_balance_weight': 0.15,
                    'hunchback_weight': 0.15,
                    'level_thresholds': {
                        'excellent': 80,
                        'good': 60,
                        'poor': 0
                    },
                    'angle_score_ranges': {
                        'excellent_range': [80, 100],
                        'good_range': [50, 80],
                        'poor_range': [0, 30],
                        'base_score': 20
                    },
                    'speed_score_ranges': {
                        'excellent_range': [80, 100],
                        'good_range': [50, 80],
                        'poor_range': [10, 50],
                        'min_score': 10,
                        'max_speed_multiplier': 1.5,
                        'min_speed_divisor': 3
                    },
                    'shoulder_balance_score_ranges': {
                        'excellent_range': [85, 100],
                        'good_range': [65, 85],
                        'poor_range': [20, 65],
                        'min_score': 20
                    },
                    'hunchback_score_ranges': {
                        'excellent_range': [85, 100],
                        'good_range': [65, 85],
                        'poor_range': [20, 65],
                        'min_score': 20
                    }
                },
                'smoothing': {
                    'speed_window': 5,
                    'score_window': 10
                }
            },
            'system': {
                'pixel_to_meter': None
            },
            'demo_mode': {
                'enabled': False,
                'indicators': {
                    'angle': {
                        'enabled': False,
                        'value_range': [60, 95],
                        'change_speed': 2.0,
                        'fluctuation': 5
                    },
                    'speed': {
                        'enabled': False,
                        'value_range': [70, 90],
                        'change_speed': 3.0,
                        'fluctuation': 8
                    },
                    'shoulder_balance': {
                        'enabled': False,
                        'value_range': [75, 100],
                        'change_speed': 1.5,
                        'fluctuation': 3
                    },
                    'hunchback': {
                        'enabled': False,
                        'value_range': [65, 85],
                        'change_speed': 2.5,
                        'fluctuation': 4
                    }
                },
                'global_settings': {
                    'update_interval': 0.1,
                    'smooth_transition': True,
                    'realistic_patterns': True,
                    'cycle_duration': 10.0
                }
            }
        }

    def evaluate_gait(self, joint_angles, speed, pose_landmarks=None):
        """
        Evaluate gait quality, returns three levels: excellent, good, poor
        Returns: (level, score, main_reason, angle_score, speed_score, shoulder_balance_score, hunchback_score)
        """
        # 檢查是否啟用Demo模式並更新Demo數值
        if self.demo_enabled:
            self._update_demo_values()

        # 計算各項評分（如果啟用Demo模式則使用Demo數值）
        angle_score = self._get_demo_or_real_score('angle', lambda: self._evaluate_angles(joint_angles))
        speed_score = self._get_demo_or_real_score('speed', lambda: self._evaluate_speed(speed))
        shoulder_balance_score = self._get_demo_or_real_score('shoulder_balance', lambda: self._evaluate_shoulder_balance(pose_landmarks))
        hunchback_score = self._get_demo_or_real_score('hunchback', lambda: self._evaluate_hunchback(pose_landmarks))

        # 綜合評分
        total_score = (angle_score * self.scoring['angle_weight'] +
                      speed_score * self.scoring['speed_weight'] +
                      shoulder_balance_score * self.scoring['shoulder_balance_weight'] +
                      hunchback_score * self.scoring['hunchback_weight'])

        # 平滑處理評分
        self.score_history.append(total_score)
        smoothed_score = np.mean(list(self.score_history))

        # 判斷等級 - 使用配置參數
        excellent_threshold = self.scoring['level_thresholds']['excellent']
        good_threshold = self.scoring['level_thresholds']['good']

        if smoothed_score >= excellent_threshold:
            level = 'excellent'
            reason = ''
        elif smoothed_score >= good_threshold:
            level = 'good'
            reason = self._get_main_issue(angle_score, speed_score, shoulder_balance_score, hunchback_score)
        else:
            level = 'poor'
            reason = self._get_main_issue(angle_score, speed_score, shoulder_balance_score, hunchback_score)

        return level, smoothed_score, reason, angle_score, speed_score, shoulder_balance_score, hunchback_score

    def _evaluate_angles(self, joint_angles):
        """Evaluate joint angles, returns 0-100 score"""
        left_knee = joint_angles.get('left_knee', None)
        right_knee = joint_angles.get('right_knee', None)

        # 如果沒有檢測到關鍵點，返回0分
        if left_knee is None and right_knee is None:
            return 0
        if left_knee == 0 and right_knee == 0:  # MediaPipe未檢測到時通常返回0
            return 0

        # 如果只檢測到一條腿，使用該腿的數據
        if left_knee is None:
            left_knee = right_knee
        if right_knee is None:
            right_knee = left_knee

        # 線性化評分系統
        excellent_ranges = self.angle_thresholds['excellent_ranges']
        good_ranges = self.angle_thresholds['good_ranges']
        poor_range = self.angle_thresholds['poor_range']

        def evaluate_single_knee(angle):
            # 獲取評分範圍配置
            score_ranges = self.scoring['angle_score_ranges']
            excellent_score_range = score_ranges['excellent_range']
            good_score_range = score_ranges['good_range']
            poor_score_range = score_ranges['poor_range']
            base_score = score_ranges['base_score']

            # 檢查是否在優良範圍
            for r in excellent_ranges:
                if r[0] <= angle <= r[1]:
                    # 在優良範圍內，線性映射到配置的分數範圍
                    range_size = r[1] - r[0]
                    position = (angle - r[0]) / range_size
                    score_range_size = excellent_score_range[1] - excellent_score_range[0]
                    return excellent_score_range[0] + position * score_range_size

            # 檢查是否在良好範圍
            for r in good_ranges:
                if r[0] <= angle <= r[1]:
                    # 在良好範圍內，線性映射到配置的分數範圍
                    range_size = r[1] - r[0]
                    position = (angle - r[0]) / range_size
                    score_range_size = good_score_range[1] - good_score_range[0]
                    return good_score_range[0] + position * score_range_size

            # 檢查是否在不佳範圍
            if poor_range[0] <= angle <= poor_range[1]:
                # 在不佳範圍內，線性映射到配置的分數範圍
                range_size = poor_range[1] - poor_range[0]
                position = (angle - poor_range[0]) / range_size
                score_range_size = poor_score_range[1] - poor_score_range[0]
                return poor_score_range[0] + position * score_range_size

            # 超出所有範圍，使用配置的基礎分數
            return base_score

        left_score = evaluate_single_knee(left_knee)
        right_score = evaluate_single_knee(right_knee)

        # 取兩腿的平均分
        return int((left_score + right_score) / 2)

    def _evaluate_speed(self, speed):
        """Evaluate speed, returns 0-100 score"""
        # 如果速度為0或接近0，返回0分
        if speed <= 0.01:  # 考慮浮點數精度
            return 0

        # 獲取速度閾值和評分範圍配置
        excellent_threshold = self.speed_thresholds['excellent']
        good_threshold = self.speed_thresholds['good']
        score_ranges = self.scoring['speed_score_ranges']

        excellent_score_range = score_ranges['excellent_range']
        good_score_range = score_ranges['good_range']
        poor_score_range = score_ranges['poor_range']
        min_score = score_ranges['min_score']
        max_speed_multiplier = score_ranges['max_speed_multiplier']
        min_speed_divisor = score_ranges['min_speed_divisor']

        if speed >= excellent_threshold:
            # 優良範圍：線性映射到配置的分數範圍
            # 超過優良閾值的部分也給予獎勵，但有上限
            max_speed = excellent_threshold * max_speed_multiplier
            if speed >= max_speed:
                return excellent_score_range[1]  # 最高分
            else:
                ratio = (speed - excellent_threshold) / (max_speed - excellent_threshold)
                score_range_size = excellent_score_range[1] - excellent_score_range[0]
                return excellent_score_range[0] + ratio * score_range_size
        elif speed >= good_threshold:
            # 良好範圍：線性映射到配置的分數範圍
            ratio = (speed - good_threshold) / (excellent_threshold - good_threshold)
            score_range_size = good_score_range[1] - good_score_range[0]
            return good_score_range[0] + ratio * score_range_size
        else:
            # 低速範圍：線性映射到配置的分數範圍
            # 使用配置的最低有意義速度
            min_speed = good_threshold / min_speed_divisor
            if speed <= min_speed:
                return min_score  # 使用配置的最低分數
            else:
                ratio = (speed - min_speed) / (good_threshold - min_speed)
                score_range_size = poor_score_range[1] - poor_score_range[0]
                return poor_score_range[0] + ratio * score_range_size

    def _evaluate_shoulder_balance(self, pose_landmarks):
        """Evaluate shoulder balance, returns 0-100 score"""
        if pose_landmarks is None:
            return 0

        # MediaPipe肩膀關鍵點
        LEFT_SHOULDER = 11
        RIGHT_SHOULDER = 12

        try:
            lm = pose_landmarks.landmark
            left_shoulder_y = lm[LEFT_SHOULDER].y
            right_shoulder_y = lm[RIGHT_SHOULDER].y

            # 計算肩膀高度差（相對差異）
            height_diff = abs(left_shoulder_y - right_shoulder_y)

            # 獲取閾值和評分範圍配置
            excellent_threshold = self.shoulder_balance_thresholds['excellent_threshold']
            good_threshold = self.shoulder_balance_thresholds['good_threshold']
            score_ranges = self.scoring['shoulder_balance_score_ranges']

            excellent_score_range = score_ranges['excellent_range']
            good_score_range = score_ranges['good_range']
            poor_score_range = score_ranges['poor_range']
            min_score = score_ranges['min_score']

            if height_diff <= excellent_threshold:
                # 優良範圍：線性映射到配置的分數範圍
                ratio = 1.0 - (height_diff / excellent_threshold)  # 差異越小分數越高
                score_range_size = excellent_score_range[1] - excellent_score_range[0]
                return int(excellent_score_range[0] + ratio * score_range_size)
            elif height_diff <= good_threshold:
                # 良好範圍：線性映射到配置的分數範圍
                ratio = 1.0 - ((height_diff - excellent_threshold) / (good_threshold - excellent_threshold))
                score_range_size = good_score_range[1] - good_score_range[0]
                return int(good_score_range[0] + ratio * score_range_size)
            else:
                # 不佳範圍：線性映射到配置的分數範圍
                # 設定最大可接受的高度差為good_threshold的2倍
                max_diff = good_threshold * 2
                if height_diff >= max_diff:
                    return min_score
                else:
                    ratio = 1.0 - ((height_diff - good_threshold) / (max_diff - good_threshold))
                    score_range_size = poor_score_range[1] - poor_score_range[0]
                    return int(poor_score_range[0] + ratio * score_range_size)

        except (AttributeError, IndexError):
            return 0

    def _evaluate_hunchback(self, pose_landmarks):
        """
        Evaluate hunchback posture using side-view analysis, returns 0-100 score
        針對側面攝影機視角的駝背檢測演算法
        """
        if pose_landmarks is None:
            return 0

        # MediaPipe關鍵點索引在各個函數中定義

        try:
            lm = pose_landmarks.landmark

            # 1. 計算頭頸角度 (Head-Neck Angle)
            head_neck_score = self._calculate_head_neck_score(lm)

            # 2. 計算CVT角度 (Craniovertebral Angle)
            cvt_score = self._calculate_cvt_score(lm)

            # 3. 計算前頭姿勢程度 (Forward Head Posture)
            fhp_score = self._calculate_forward_head_posture_score(lm)

            # 4. 計算圓肩程度 (Rounded Shoulders)
            rounded_shoulder_score = self._calculate_rounded_shoulders_score(lm)

            # 加權平均計算總分（側面視角專用權重）
            weights = {
                'head_neck': 0.35,      # 頭頸角度最重要
                'cvt': 0.30,            # CVT角度次之
                'forward_head': 0.25,   # 前頭姿勢
                'rounded_shoulder': 0.10 # 圓肩程度
            }

            total_score = (head_neck_score * weights['head_neck'] +
                          cvt_score * weights['cvt'] +
                          fhp_score * weights['forward_head'] +
                          rounded_shoulder_score * weights['rounded_shoulder'])

            return int(total_score)

        except (AttributeError, IndexError, ZeroDivisionError):
            return 0

    def _calculate_head_neck_score(self, lm):
        """計算頭頸角度評分"""
        try:
            # MediaPipe關鍵點索引
            RIGHT_EAR = 8
            RIGHT_SHOULDER = 12
            RIGHT_HIP = 24

            # 選擇較靠近攝影機的一側（通常是右側）
            ear = lm[RIGHT_EAR]
            shoulder = lm[RIGHT_SHOULDER]
            hip = lm[RIGHT_HIP]

            # 計算向量
            vec1 = np.array([ear.x - shoulder.x, ear.y - shoulder.y])
            vec2 = np.array([hip.x - shoulder.x, hip.y - shoulder.y])

            # 計算角度
            angle = self._calculate_angle_between_vectors(vec1, vec2)

            # 評分標準（角度，單位：度）
            if 45 <= angle <= 60:  # 正常範圍
                return 100
            elif 35 <= angle <= 70:  # 輕微異常
                return 75
            else:  # 嚴重異常
                return 40

        except (AttributeError, IndexError, ZeroDivisionError):
            return 0

    def _calculate_cvt_score(self, lm):
        """計算CVT角度評分 (Craniovertebral Angle)"""
        try:
            # MediaPipe關鍵點索引
            RIGHT_EAR = 8
            LEFT_SHOULDER = 11
            RIGHT_SHOULDER = 12

            # 使用耳朵、肩膀頂點、第7頸椎點（用肩膀中心代替）
            ear = lm[RIGHT_EAR]
            shoulder = lm[RIGHT_SHOULDER]
            left_shoulder = lm[LEFT_SHOULDER]

            # 計算肩膀中心作為頸椎參考點
            neck_base_x = (shoulder.x + left_shoulder.x) / 2
            neck_base_y = (shoulder.y + left_shoulder.y) / 2

            # 計算水平線向量和耳朵-頸椎向量
            horizontal_vec = np.array([1, 0])
            ear_neck_vec = np.array([ear.x - neck_base_x, ear.y - neck_base_y])

            angle = self._calculate_angle_between_vectors(horizontal_vec, ear_neck_vec)

            # CVT角度評分標準
            if angle >= 48:  # 正常CVT角度
                return 100
            elif angle >= 42:  # 輕微前頭姿勢
                return 75
            else:  # 嚴重前頭姿勢
                return 40

        except (AttributeError, IndexError, ZeroDivisionError):
            return 0

    def _calculate_forward_head_posture_score(self, lm):
        """計算前頭姿勢評分"""
        try:
            # MediaPipe關鍵點索引
            NOSE = 0
            LEFT_SHOULDER = 11
            RIGHT_SHOULDER = 12


            # 頭部位置（使用鼻子）
            head = lm[NOSE]

            # 身體軸線（肩膀中心）
            left_shoulder = lm[LEFT_SHOULDER]
            right_shoulder = lm[RIGHT_SHOULDER]

            shoulder_center_x = (left_shoulder.x + right_shoulder.x) / 2

            # 理想頭部位置（在身體軸線上）
            ideal_head_x = shoulder_center_x

            # 計算前移比例
            forward_displacement = head.x - ideal_head_x
            body_width = abs(left_shoulder.x - right_shoulder.x)

            if body_width > 0:
                forward_ratio = abs(forward_displacement) / body_width
            else:
                forward_ratio = 0

            # 前頭姿勢評分標準
            if forward_ratio <= 0.1:
                return 100
            elif forward_ratio <= 0.2:
                return 75
            else:
                return 40

        except (AttributeError, IndexError, ZeroDivisionError):
            return 0

    def _calculate_rounded_shoulders_score(self, lm):
        """計算圓肩程度評分"""
        try:
            # MediaPipe關鍵點索引
            RIGHT_SHOULDER = 12
            RIGHT_ELBOW = 14
            RIGHT_HIP = 24

            # 使用肩膀和肘部的位置關係
            shoulder = lm[RIGHT_SHOULDER]
            elbow = lm[RIGHT_ELBOW]
            hip = lm[RIGHT_HIP]

            # 計算肩膀-髖部的垂直線
            vertical_vec = np.array([0, hip.y - shoulder.y])
            shoulder_elbow_vec = np.array([elbow.x - shoulder.x, elbow.y - shoulder.y])

            angle = self._calculate_angle_between_vectors(vertical_vec, shoulder_elbow_vec)

            # 圓肩評分標準
            if 80 <= angle <= 100:  # 接近垂直
                return 100
            elif 70 <= angle <= 110:
                return 75
            else:
                return 40

        except (AttributeError, IndexError, ZeroDivisionError):
            return 0

    def _calculate_angle_between_vectors(self, vec1, vec2):
        """計算兩個向量之間的角度（度）"""
        try:
            cos_angle = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
            cos_angle = np.clip(cos_angle, -1.0, 1.0)
            angle_rad = np.arccos(cos_angle)
            return math.degrees(angle_rad)
        except (ZeroDivisionError, ValueError):
            return 0

    def _get_demo_or_real_score(self, indicator_name, real_score_func):
        """根據Demo模式設定返回Demo數值或真實計算數值"""
        if (self.demo_enabled and
            self.demo_indicators.get(indicator_name, {}).get('enabled', False)):
            return int(self.demo_values[indicator_name])
        else:
            return real_score_func()

    def _update_demo_values(self):
        """更新Demo模式的數值"""
        current_time = time.time()
        update_interval = self.demo_global_settings.get('update_interval', 0.1)

        # 檢查是否需要更新
        if current_time - self.last_demo_update < update_interval:
            return

        self.last_demo_update = current_time

        # 更新每個指標的Demo數值
        for indicator_name in ['angle', 'speed', 'shoulder_balance', 'hunchback']:
            indicator_config = self.demo_indicators.get(indicator_name, {})
            if indicator_config.get('enabled', False):
                self._update_single_demo_value(indicator_name, indicator_config, current_time)

    def _update_single_demo_value(self, indicator_name, config, current_time):
        """更新單個指標的Demo數值"""
        value_range = config.get('value_range', [50, 90])
        change_speed = config.get('change_speed', 2.0)
        fluctuation = config.get('fluctuation', 5)

        min_val, max_val = value_range
        current_value = self.demo_values[indicator_name]
        target_value = self.demo_targets[indicator_name]

        # 檢查是否需要新的目標值
        if abs(current_value - target_value) < 2.0:
            # 生成新的目標值
            if self.demo_global_settings.get('realistic_patterns', True):
                # 使用週期性模式
                cycle_duration = self.demo_global_settings.get('cycle_duration', 10.0)
                cycle_progress = ((current_time - self.demo_cycle_start) % cycle_duration) / cycle_duration

                # 使用正弦波創建週期性變化
                base_target = min_val + (max_val - min_val) * (0.5 + 0.3 * math.sin(2 * math.pi * cycle_progress))

                # 添加隨機變化
                random_offset = random.uniform(-fluctuation, fluctuation)
                target_value = max(min_val, min(max_val, base_target + random_offset))
            else:
                # 簡單隨機目標
                target_value = random.uniform(min_val, max_val)

            self.demo_targets[indicator_name] = target_value

        # 平滑移動到目標值
        direction = 1 if target_value > current_value else -1
        max_change = change_speed * self.demo_global_settings.get('update_interval', 0.1)

        if self.demo_global_settings.get('smooth_transition', True):
            # 平滑過渡
            change = min(abs(target_value - current_value), max_change) * direction
        else:
            # 直接跳躍
            change = (target_value - current_value) * 0.1

        # 添加小幅隨機波動
        small_fluctuation = random.uniform(-fluctuation * 0.2, fluctuation * 0.2)
        new_value = current_value + change + small_fluctuation

        # 確保數值在範圍內
        new_value = max(0, min(100, new_value))
        new_value = max(min_val, min(max_val, new_value))

        self.demo_values[indicator_name] = new_value

    def _get_main_issue(self, angle_score, speed_score, shoulder_balance_score=None, hunchback_score=None):
        """Determine main issue source"""
        scores = {'Angle': angle_score, 'Speed': speed_score}

        if shoulder_balance_score is not None:
            scores['Shoulder Balance'] = shoulder_balance_score
        if hunchback_score is not None:
            scores['Hunchback'] = hunchback_score

        # 找出最低分的項目
        min_score = min(scores.values())
        main_issues = [name for name, score in scores.items() if score == min_score]

        if len(main_issues) == 1:
            return main_issues[0]
        elif len(main_issues) == len(scores):
            return 'All'
        else:
            return ', '.join(main_issues)

    def calculate_speed(self, pose_landmarks, dt):
        """Calculate walking speed using average of both hips for better stability"""
        # Use both left and right hips for more stable tracking
        LEFT_HIP = 23
        RIGHT_HIP = 24
        
        if pose_landmarks is None:
            return 0.0

        lm = pose_landmarks.landmark
        
        # Get both hip positions
        left_x, left_y = lm[LEFT_HIP].x, lm[LEFT_HIP].y
        right_x, right_y = lm[RIGHT_HIP].x, lm[RIGHT_HIP].y
        
        # Calculate average position of both hips
        avg_x = (left_x + right_x) / 2
        avg_y = (left_y + right_y) / 2
        
        # Store the average position
        self.prev_positions.append((avg_x, avg_y))

        if len(self.prev_positions) < 2:
            return 0.0

        # Calculate movement distance
        dx = self.prev_positions[-1][0] - self.prev_positions[0][0]
        dy = self.prev_positions[-1][1] - self.prev_positions[0][1]
        dist = np.sqrt(dx**2 + dy**2)

        # Convert to actual distance if calibration is available
        if self.pixel_to_meter:
            dist = dist * self.pixel_to_meter

        # Calculate speed
        time_span = dt * (len(self.prev_positions) - 1)
        speed = dist / time_span if time_span > 0 else 0.0

        return speed

    # Backward compatibility method
    def is_normal_gait(self, joint_angles, speed, pose_landmarks=None):
        """Backward compatibility method, converts new evaluation to binary result"""
        level, _, reason, _, _, _, _ = self.evaluate_gait(joint_angles, speed, pose_landmarks)
        is_normal = level in ['excellent', 'good']
        return is_normal, reason