import numpy as np
from collections import deque

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("Warning: PyYAML not installed, using default configuration")

class GaitAnalyzer:
    def __init__(self, config_path=None):
        # 自動檢測配置檔案路徑
        if config_path is None:
            import os
            # 嘗試不同的路徑
            possible_paths = [
                'settings.yaml',           # 當前目錄
                '../settings.yaml',        # 上一級目錄
                os.path.join(os.path.dirname(__file__), '..', 'settings.yaml')  # 相對於此檔案的上級目錄
            ]
            config_path = '../settings.yaml'  # 預設路徑
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    break
        # 載入配置檔案
        self.config = self._load_config(config_path)

        # 從配置中獲取參數
        gait_config = self.config['gait_analysis']
        self.angle_thresholds = gait_config['angle_thresholds']
        self.speed_thresholds_config = gait_config['speed_thresholds']
        self.shoulder_balance_thresholds = gait_config['shoulder_balance_thresholds']
        self.hunchback_thresholds = gait_config['hunchback_thresholds']
        self.scoring = gait_config['scoring']

        # 平滑處理參數
        smoothing = gait_config['smoothing']
        self.speed_window = smoothing['speed_window']
        self.score_window = smoothing['score_window']

        # 系統參數
        self.pixel_to_meter = self.config['system']['pixel_to_meter']

        # 根據 pixel_to_meter 設定選擇適當的速度閾值
        self._update_speed_thresholds()

        # 初始化數據緩存
        self.prev_positions = deque(maxlen=self.speed_window)
        self.score_history = deque(maxlen=self.score_window)

    def _update_speed_thresholds(self):
        """根據 pixel_to_meter 設定選擇適當的速度閾值"""
        if self.pixel_to_meter is not None:
            # 使用實際速度閾值（公尺/秒）
            self.speed_thresholds = self.speed_thresholds_config['meters_per_second']
            self.speed_unit = "m/s"
            print(f"使用實際速度閾值: excellent={self.speed_thresholds['excellent']} m/s, good={self.speed_thresholds['good']} m/s")
        else:
            # 使用相對速度閾值（像素/秒）
            self.speed_thresholds = self.speed_thresholds_config['pixels_per_second']
            self.speed_unit = "pixels/s"
            print(f"使用相對速度閾值: excellent={self.speed_thresholds['excellent']} pixels/s, good={self.speed_thresholds['good']} pixels/s")

    def get_speed_unit(self):
        """獲取當前使用的速度單位"""
        return self.speed_unit

    def get_speed_thresholds(self):
        """獲取當前使用的速度閾值"""
        return self.speed_thresholds

    def _load_config(self, config_path):
        """Load YAML configuration file"""
        if not YAML_AVAILABLE:
            print("PyYAML not installed, using default configuration")
            return self._get_default_config()

        try:
            with open(config_path, 'r', encoding='utf-8') as file:
                return yaml.safe_load(file)
        except FileNotFoundError:
            print(f"Configuration file {config_path} not found, using default values")
            return self._get_default_config()
        except Exception as e:
            print(f"Configuration file parsing error: {e}, using default values")
            return self._get_default_config()

    def _get_default_config(self):
        """Return default configuration"""
        return {
            'gait_analysis': {
                'angle_thresholds': {
                    'excellent_ranges': [[60, 100], [160, 180]],
                    'good_ranges': [[100, 130], [130, 160]],
                    'poor_range': [130, 160]
                },
                'speed_thresholds': {
                    'meters_per_second': {
                        'excellent': 1.1,
                        'good': 0.8
                    },
                    'pixels_per_second': {
                        'excellent': 0.25,
                        'good': 0.12
                    }
                },
                'shoulder_balance_thresholds': {
                    'excellent_threshold': 0.02,
                    'good_threshold': 0.05
                },
                'hunchback_thresholds': {
                    'excellent_threshold': 0.95,
                    'good_threshold': 0.85
                },
                'scoring': {
                    'angle_weight': 0.25,
                    'speed_weight': 0.45,
                    'shoulder_balance_weight': 0.15,
                    'hunchback_weight': 0.15,
                    'level_thresholds': {
                        'excellent': 80,
                        'good': 60,
                        'poor': 0
                    },
                    'angle_score_ranges': {
                        'excellent_range': [80, 100],
                        'good_range': [50, 80],
                        'poor_range': [0, 30],
                        'base_score': 20
                    },
                    'speed_score_ranges': {
                        'excellent_range': [80, 100],
                        'good_range': [50, 80],
                        'poor_range': [10, 50],
                        'min_score': 10,
                        'max_speed_multiplier': 1.5,
                        'min_speed_divisor': 3
                    },
                    'shoulder_balance_score_ranges': {
                        'excellent_range': [85, 100],
                        'good_range': [65, 85],
                        'poor_range': [20, 65],
                        'min_score': 20
                    },
                    'hunchback_score_ranges': {
                        'excellent_range': [85, 100],
                        'good_range': [65, 85],
                        'poor_range': [20, 65],
                        'min_score': 20
                    }
                },
                'smoothing': {
                    'speed_window': 5,
                    'score_window': 10
                }
            },
            'system': {
                'pixel_to_meter': None
            }
        }

    def evaluate_gait(self, joint_angles, speed, pose_landmarks=None):
        """
        Evaluate gait quality, returns three levels: excellent, good, poor
        Returns: (level, score, main_reason, angle_score, speed_score, shoulder_balance_score, hunchback_score)
        """
        # 計算角度評分
        angle_score = self._evaluate_angles(joint_angles)

        # 計算速度評分
        speed_score = self._evaluate_speed(speed)

        # 計算肩膀平衡評分
        shoulder_balance_score = self._evaluate_shoulder_balance(pose_landmarks)

        # 計算駝背檢測評分
        hunchback_score = self._evaluate_hunchback(pose_landmarks)

        # 綜合評分
        total_score = (angle_score * self.scoring['angle_weight'] +
                      speed_score * self.scoring['speed_weight'] +
                      shoulder_balance_score * self.scoring['shoulder_balance_weight'] +
                      hunchback_score * self.scoring['hunchback_weight'])

        # 平滑處理評分
        self.score_history.append(total_score)
        smoothed_score = np.mean(list(self.score_history))

        # 判斷等級 - 使用配置參數
        excellent_threshold = self.scoring['level_thresholds']['excellent']
        good_threshold = self.scoring['level_thresholds']['good']

        if smoothed_score >= excellent_threshold:
            level = 'excellent'
            reason = ''
        elif smoothed_score >= good_threshold:
            level = 'good'
            reason = self._get_main_issue(angle_score, speed_score, shoulder_balance_score, hunchback_score)
        else:
            level = 'poor'
            reason = self._get_main_issue(angle_score, speed_score, shoulder_balance_score, hunchback_score)

        return level, smoothed_score, reason, angle_score, speed_score, shoulder_balance_score, hunchback_score

    def _evaluate_angles(self, joint_angles):
        """Evaluate joint angles, returns 0-100 score"""
        left_knee = joint_angles.get('left_knee', None)
        right_knee = joint_angles.get('right_knee', None)

        # 如果沒有檢測到關鍵點，返回0分
        if left_knee is None and right_knee is None:
            return 0
        if left_knee == 0 and right_knee == 0:  # MediaPipe未檢測到時通常返回0
            return 0

        # 如果只檢測到一條腿，使用該腿的數據
        if left_knee is None:
            left_knee = right_knee
        if right_knee is None:
            right_knee = left_knee

        # 線性化評分系統
        excellent_ranges = self.angle_thresholds['excellent_ranges']
        good_ranges = self.angle_thresholds['good_ranges']
        poor_range = self.angle_thresholds['poor_range']

        def evaluate_single_knee(angle):
            # 獲取評分範圍配置
            score_ranges = self.scoring['angle_score_ranges']
            excellent_score_range = score_ranges['excellent_range']
            good_score_range = score_ranges['good_range']
            poor_score_range = score_ranges['poor_range']
            base_score = score_ranges['base_score']

            # 檢查是否在優良範圍
            for r in excellent_ranges:
                if r[0] <= angle <= r[1]:
                    # 在優良範圍內，線性映射到配置的分數範圍
                    range_size = r[1] - r[0]
                    position = (angle - r[0]) / range_size
                    score_range_size = excellent_score_range[1] - excellent_score_range[0]
                    return excellent_score_range[0] + position * score_range_size

            # 檢查是否在良好範圍
            for r in good_ranges:
                if r[0] <= angle <= r[1]:
                    # 在良好範圍內，線性映射到配置的分數範圍
                    range_size = r[1] - r[0]
                    position = (angle - r[0]) / range_size
                    score_range_size = good_score_range[1] - good_score_range[0]
                    return good_score_range[0] + position * score_range_size

            # 檢查是否在不佳範圍
            if poor_range[0] <= angle <= poor_range[1]:
                # 在不佳範圍內，線性映射到配置的分數範圍
                range_size = poor_range[1] - poor_range[0]
                position = (angle - poor_range[0]) / range_size
                score_range_size = poor_score_range[1] - poor_score_range[0]
                return poor_score_range[0] + position * score_range_size

            # 超出所有範圍，使用配置的基礎分數
            return base_score

        left_score = evaluate_single_knee(left_knee)
        right_score = evaluate_single_knee(right_knee)

        # 取兩腿的平均分
        return int((left_score + right_score) / 2)

    def _evaluate_speed(self, speed):
        """Evaluate speed, returns 0-100 score"""
        # 如果速度為0或接近0，返回0分
        if speed <= 0.01:  # 考慮浮點數精度
            return 0

        # 獲取速度閾值和評分範圍配置
        excellent_threshold = self.speed_thresholds['excellent']
        good_threshold = self.speed_thresholds['good']
        score_ranges = self.scoring['speed_score_ranges']

        excellent_score_range = score_ranges['excellent_range']
        good_score_range = score_ranges['good_range']
        poor_score_range = score_ranges['poor_range']
        min_score = score_ranges['min_score']
        max_speed_multiplier = score_ranges['max_speed_multiplier']
        min_speed_divisor = score_ranges['min_speed_divisor']

        if speed >= excellent_threshold:
            # 優良範圍：線性映射到配置的分數範圍
            # 超過優良閾值的部分也給予獎勵，但有上限
            max_speed = excellent_threshold * max_speed_multiplier
            if speed >= max_speed:
                return excellent_score_range[1]  # 最高分
            else:
                ratio = (speed - excellent_threshold) / (max_speed - excellent_threshold)
                score_range_size = excellent_score_range[1] - excellent_score_range[0]
                return excellent_score_range[0] + ratio * score_range_size
        elif speed >= good_threshold:
            # 良好範圍：線性映射到配置的分數範圍
            ratio = (speed - good_threshold) / (excellent_threshold - good_threshold)
            score_range_size = good_score_range[1] - good_score_range[0]
            return good_score_range[0] + ratio * score_range_size
        else:
            # 低速範圍：線性映射到配置的分數範圍
            # 使用配置的最低有意義速度
            min_speed = good_threshold / min_speed_divisor
            if speed <= min_speed:
                return min_score  # 使用配置的最低分數
            else:
                ratio = (speed - min_speed) / (good_threshold - min_speed)
                score_range_size = poor_score_range[1] - poor_score_range[0]
                return poor_score_range[0] + ratio * score_range_size

    def _evaluate_shoulder_balance(self, pose_landmarks):
        """Evaluate shoulder balance, returns 0-100 score"""
        if pose_landmarks is None:
            return 0

        # MediaPipe肩膀關鍵點
        LEFT_SHOULDER = 11
        RIGHT_SHOULDER = 12

        try:
            lm = pose_landmarks.landmark
            left_shoulder_y = lm[LEFT_SHOULDER].y
            right_shoulder_y = lm[RIGHT_SHOULDER].y

            # 計算肩膀高度差（相對差異）
            height_diff = abs(left_shoulder_y - right_shoulder_y)

            # 獲取閾值和評分範圍配置
            excellent_threshold = self.shoulder_balance_thresholds['excellent_threshold']
            good_threshold = self.shoulder_balance_thresholds['good_threshold']
            score_ranges = self.scoring['shoulder_balance_score_ranges']

            excellent_score_range = score_ranges['excellent_range']
            good_score_range = score_ranges['good_range']
            poor_score_range = score_ranges['poor_range']
            min_score = score_ranges['min_score']

            if height_diff <= excellent_threshold:
                # 優良範圍：線性映射到配置的分數範圍
                ratio = 1.0 - (height_diff / excellent_threshold)  # 差異越小分數越高
                score_range_size = excellent_score_range[1] - excellent_score_range[0]
                return int(excellent_score_range[0] + ratio * score_range_size)
            elif height_diff <= good_threshold:
                # 良好範圍：線性映射到配置的分數範圍
                ratio = 1.0 - ((height_diff - excellent_threshold) / (good_threshold - excellent_threshold))
                score_range_size = good_score_range[1] - good_score_range[0]
                return int(good_score_range[0] + ratio * score_range_size)
            else:
                # 不佳範圍：線性映射到配置的分數範圍
                # 設定最大可接受的高度差為good_threshold的2倍
                max_diff = good_threshold * 2
                if height_diff >= max_diff:
                    return min_score
                else:
                    ratio = 1.0 - ((height_diff - good_threshold) / (max_diff - good_threshold))
                    score_range_size = poor_score_range[1] - poor_score_range[0]
                    return int(poor_score_range[0] + ratio * score_range_size)

        except (AttributeError, IndexError):
            return 0

    def _evaluate_hunchback(self, pose_landmarks):
        """Evaluate hunchback posture, returns 0-100 score"""
        if pose_landmarks is None:
            return 0

        # MediaPipe關鍵點
        NOSE = 0
        LEFT_SHOULDER = 11
        RIGHT_SHOULDER = 12
        LEFT_HIP = 23
        RIGHT_HIP = 24

        try:
            lm = pose_landmarks.landmark

            # 計算頭部位置（鼻子）
            nose_x = lm[NOSE].x

            # 計算肩膀中心位置
            shoulder_center_x = (lm[LEFT_SHOULDER].x + lm[RIGHT_SHOULDER].x) / 2
            shoulder_center_y = (lm[LEFT_SHOULDER].y + lm[RIGHT_SHOULDER].y) / 2

            # 計算髖部中心位置
            hip_center_x = (lm[LEFT_HIP].x + lm[RIGHT_HIP].x) / 2
            hip_center_y = (lm[LEFT_HIP].y + lm[RIGHT_HIP].y) / 2

            # 計算理想的頭部位置（肩膀和髖部的垂直線上）
            ideal_head_x = shoulder_center_x

            # 計算身體長度（肩膀到髖部的距離）
            body_length = np.sqrt((shoulder_center_x - hip_center_x)**2 + (shoulder_center_y - hip_center_y)**2)

            # 計算頭部前傾程度（相對於身體長度的比例）
            head_forward_ratio = abs(nose_x - ideal_head_x) / body_length if body_length > 0 else 0

            # 計算對齊度（1.0表示完全對齊，0.0表示完全不對齊）
            alignment_ratio = max(0, 1.0 - head_forward_ratio * 2)  # 乘以2增加敏感度

            # 獲取閾值和評分範圍配置
            excellent_threshold = self.hunchback_thresholds['excellent_threshold']
            good_threshold = self.hunchback_thresholds['good_threshold']
            score_ranges = self.scoring['hunchback_score_ranges']

            excellent_score_range = score_ranges['excellent_range']
            good_score_range = score_ranges['good_range']
            poor_score_range = score_ranges['poor_range']
            min_score = score_ranges['min_score']

            if alignment_ratio >= excellent_threshold:
                # 優良範圍：線性映射到配置的分數範圍
                ratio = (alignment_ratio - excellent_threshold) / (1.0 - excellent_threshold)
                score_range_size = excellent_score_range[1] - excellent_score_range[0]
                return int(excellent_score_range[0] + ratio * score_range_size)
            elif alignment_ratio >= good_threshold:
                # 良好範圍：線性映射到配置的分數範圍
                ratio = (alignment_ratio - good_threshold) / (excellent_threshold - good_threshold)
                score_range_size = good_score_range[1] - good_score_range[0]
                return int(good_score_range[0] + ratio * score_range_size)
            else:
                # 不佳範圍：線性映射到配置的分數範圍
                if alignment_ratio <= 0:
                    return min_score
                ratio = alignment_ratio / good_threshold
                score_range_size = poor_score_range[1] - poor_score_range[0]
                return int(poor_score_range[0] + ratio * score_range_size)

        except (AttributeError, IndexError):
            return 0

    def _get_main_issue(self, angle_score, speed_score, shoulder_balance_score=None, hunchback_score=None):
        """Determine main issue source"""
        scores = {'Angle': angle_score, 'Speed': speed_score}

        if shoulder_balance_score is not None:
            scores['Shoulder Balance'] = shoulder_balance_score
        if hunchback_score is not None:
            scores['Hunchback'] = hunchback_score

        # 找出最低分的項目
        min_score = min(scores.values())
        main_issues = [name for name, score in scores.items() if score == min_score]

        if len(main_issues) == 1:
            return main_issues[0]
        elif len(main_issues) == len(scores):
            return 'All'
        else:
            return ', '.join(main_issues)

    def calculate_speed(self, pose_landmarks, dt):
        """Calculate walking speed using average of both hips for better stability"""
        # Use both left and right hips for more stable tracking
        LEFT_HIP = 23
        RIGHT_HIP = 24
        
        if pose_landmarks is None:
            return 0.0

        lm = pose_landmarks.landmark
        
        # Get both hip positions
        left_x, left_y = lm[LEFT_HIP].x, lm[LEFT_HIP].y
        right_x, right_y = lm[RIGHT_HIP].x, lm[RIGHT_HIP].y
        
        # Calculate average position of both hips
        avg_x = (left_x + right_x) / 2
        avg_y = (left_y + right_y) / 2
        
        # Store the average position
        self.prev_positions.append((avg_x, avg_y))

        if len(self.prev_positions) < 2:
            return 0.0

        # Calculate movement distance
        dx = self.prev_positions[-1][0] - self.prev_positions[0][0]
        dy = self.prev_positions[-1][1] - self.prev_positions[0][1]
        dist = np.sqrt(dx**2 + dy**2)

        # Convert to actual distance if calibration is available
        if self.pixel_to_meter:
            dist = dist * self.pixel_to_meter

        # Calculate speed
        time_span = dt * (len(self.prev_positions) - 1)
        speed = dist / time_span if time_span > 0 else 0.0

        return speed

    # Backward compatibility method
    def is_normal_gait(self, joint_angles, speed, pose_landmarks=None):
        """Backward compatibility method, converts new evaluation to binary result"""
        level, _, reason, _, _, _, _ = self.evaluate_gait(joint_angles, speed, pose_landmarks)
        is_normal = level in ['excellent', 'good']
        return is_normal, reason