#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
PySide6 GUI Main Application for Gait Detection System
"""

import sys
import cv2
import numpy as np
import time
import yaml
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                               QHBoxLayout, QGridLayout, QLabel, QProgressBar,
                               QFrame, QStatusBar, QSizePolicy, QCheckBox, QDialog,
                               QPushButton)
from PySide6.QtCore import QTimer, Qt, Signal, QThread
from PySide6.QtGui import QImage, QPixmap, QFont, QPalette, QColor

# Import our modules
from pose_detection import PoseDetector
from angle_calculator import AngleCalculator
from gait_analyzer import GaitAnalyzer
import videocapture as vc
from gui_widgets import GradientProgressBar, StatusPanel


class VideoThread(QThread):
    """Thread for handling video processing"""
    frame_ready = Signal(np.ndarray)
    gait_data_ready = Signal(dict)
    auto_scoring_result = Signal(dict)  # Signal for auto scoring results
    dialog_closed = Signal()  # Signal for when dialog is closed

    def __init__(self):
        super().__init__()
        self.running = False
        self.cap = None
        self.pose_detector = PoseDetector()
        self.angle_calculator = AngleCalculator()
        self.gait_analyzer = GaitAnalyzer()
        self.prev_time = time.time()

        # Auto scoring related attributes
        self.auto_scoring_enabled = False
        self.person_detected_frames = 0
        self.person_absent_frames = 0
        self.scoring_session_active = False
        self.dialog_showing = False  # Track if result dialog is showing
        self.angle_scores = []
        self.speed_scores = []
        self.shoulder_balance_scores = []
        self.hunchback_scores = []
        self.session_start_time = None
        
    def start_capture(self):
        """Start video capture"""
        self.cap = vc.open_camera()
        self.running = True
        self.start()
        
    def stop_capture(self):
        """Stop video capture"""
        self.running = False
        if self.cap:
            self.cap.release()
        self.quit()
        self.wait()
        
    def run(self):
        """Main video processing loop"""
        while self.running:
            if self.cap is None:
                continue
                
            frame = vc.get_camera_frame(self.cap)
            if frame is None:
                continue
                
            # Mirror the frame horizontally
            frame = cv2.flip(frame, 1)
            
            # Detect pose
            pose_landmarks = self.pose_detector.detect(frame)
            
            # Prepare gait data
            gait_data = {
                'pose_landmarks': pose_landmarks,
                'has_person': pose_landmarks is not None,
                'angles': {},
                'speed': 0.0,
                'level': 'poor',
                'score': 0.0,
                'angle_score': 0.0,
                'speed_score': 0.0,
                'reason': ''
            }
            
            if pose_landmarks is not None:
                # Calculate angles
                angles = self.angle_calculator.get_joint_angles(pose_landmarks)
                gait_data['angles'] = angles
                
                # Calculate speed
                curr_time = time.time()
                dt = curr_time - self.prev_time
                self.prev_time = curr_time
                speed = self.gait_analyzer.calculate_speed(pose_landmarks, dt)
                gait_data['speed'] = speed
                
                # Evaluate gait
                level, score, reason, angle_score, speed_score, shoulder_balance_score, hunchback_score = self.gait_analyzer.evaluate_gait(angles, speed, pose_landmarks)

                gait_data.update({
                    'level': level,
                    'score': score,
                    'angle_score': angle_score,
                    'speed_score': speed_score,
                    'shoulder_balance_score': shoulder_balance_score,
                    'hunchback_score': hunchback_score,
                    'reason': reason
                })

                # Handle auto scoring if enabled
                if self.auto_scoring_enabled:
                    self.handle_auto_scoring(True, angle_score, speed_score, shoulder_balance_score, hunchback_score)
            else:
                # Handle auto scoring for no person detected
                if self.auto_scoring_enabled:
                    self.handle_auto_scoring(False, 0, 0, 0, 0)

            # Emit signals
            self.frame_ready.emit(frame)
            self.gait_data_ready.emit(gait_data)
            
            # Small delay to prevent overwhelming the GUI
            self.msleep(33)  # ~30 FPS

    def set_auto_scoring_enabled(self, enabled):
        """Enable or disable auto scoring"""
        self.auto_scoring_enabled = enabled
        if not enabled:
            self.reset_scoring_session()

    def handle_auto_scoring(self, person_detected, angle_score, speed_score, shoulder_balance_score, hunchback_score):
        """Handle auto scoring logic"""
        if person_detected:
            self.person_detected_frames += 1
            self.person_absent_frames = 0

            # Check if we should start a scoring session
            # Don't start new session if dialog is showing
            if (not self.scoring_session_active and
                not self.dialog_showing and
                self.person_detected_frames >= 5):  # enter_threshold
                print("Auto scoring: Session started - collecting gait data...")
                self.start_scoring_session()

            # Collect scores if session is active
            if self.scoring_session_active:
                self.angle_scores.append(angle_score)
                self.speed_scores.append(speed_score)
                self.shoulder_balance_scores.append(shoulder_balance_score)
                self.hunchback_scores.append(hunchback_score)
        else:
            self.person_absent_frames += 1
            self.person_detected_frames = 0

            # Check if we should end the scoring session
            if self.scoring_session_active and self.person_absent_frames >= 10:  # exit_threshold
                print("Auto scoring: Person left - calculating final score...")
                self.end_scoring_session()

            # Check if dialog was closed but person still present, now person left
            elif (not self.scoring_session_active and
                  not self.dialog_showing and
                  self.person_absent_frames >= 5 and
                  (self.angle_scores or self.speed_scores or self.shoulder_balance_scores or self.hunchback_scores)):  # Had previous session data
                print("Auto scoring: Person left after dialog closed - ready for next session")
                self.reset_scoring_session()

    def start_scoring_session(self):
        """Start a new scoring session"""
        self.scoring_session_active = True
        self.angle_scores = []
        self.speed_scores = []
        self.shoulder_balance_scores = []
        self.hunchback_scores = []
        self.session_start_time = time.time()

    def end_scoring_session(self):
        """End the current scoring session and calculate results"""
        if not self.scoring_session_active:
            return

        self.scoring_session_active = False

        # Check if we have enough data
        session_duration = time.time() - self.session_start_time
        if session_duration < 3.0 or len(self.angle_scores) < 10:  # min_session_duration and min_samples
            self.reset_scoring_session()
            return

        # Calculate average scores
        avg_angle_score = sum(self.angle_scores) / len(self.angle_scores)
        avg_speed_score = sum(self.speed_scores) / len(self.speed_scores)
        avg_shoulder_balance_score = sum(self.shoulder_balance_scores) / len(self.shoulder_balance_scores)
        avg_hunchback_score = sum(self.hunchback_scores) / len(self.hunchback_scores)

        # Calculate final score with weights from settings.yaml
        angle_weight = self.gait_analyzer.scoring['angle_weight']
        speed_weight = self.gait_analyzer.scoring['speed_weight']
        shoulder_balance_weight = self.gait_analyzer.scoring['shoulder_balance_weight']
        hunchback_weight = self.gait_analyzer.scoring['hunchback_weight']
        final_score = (avg_angle_score * angle_weight +
                      avg_speed_score * speed_weight +
                      avg_shoulder_balance_score * shoulder_balance_weight +
                      avg_hunchback_score * hunchback_weight)

        # Determine score level - 使用配置參數
        excellent_threshold = self.gait_analyzer.scoring['level_thresholds']['excellent']
        good_threshold = self.gait_analyzer.scoring['level_thresholds']['good']

        if final_score >= excellent_threshold:
            level = 'excellent'
        elif final_score >= good_threshold:
            level = 'good'
        else:
            level = 'poor'

        # Emit result
        result = {
            'final_score': final_score,
            'avg_angle_score': avg_angle_score,
            'avg_speed_score': avg_speed_score,
            'avg_shoulder_balance_score': avg_shoulder_balance_score,
            'avg_hunchback_score': avg_hunchback_score,
            'level': level,
            'session_duration': session_duration,
            'sample_count': len(self.angle_scores)
        }

        print(f"Auto scoring: Final score {final_score:.1f} ({level}) - showing result dialog")
        self.dialog_showing = True  # Mark dialog as showing
        self.auto_scoring_result.emit(result)
        # Don't reset session here - wait for dialog to close

    def reset_scoring_session(self):
        """Reset scoring session data"""
        self.scoring_session_active = False
        self.dialog_showing = False
        self.person_detected_frames = 0
        self.person_absent_frames = 0
        self.angle_scores = []
        self.speed_scores = []
        self.shoulder_balance_scores = []
        self.hunchback_scores = []
        self.session_start_time = None

    def on_dialog_closed(self):
        """Handle dialog closed event"""
        print("Auto scoring: Dialog closed")
        self.dialog_showing = False

        # If no person is detected, reset and ready for next session
        if self.person_absent_frames >= 5:
            print("Auto scoring: No person detected, ready for next session")
            self.reset_scoring_session()
        else:
            print("Auto scoring: Person still detected, waiting for person to leave")
            # Keep dialog_showing as False, but don't reset until person leaves


class MainWindow(QMainWindow):
    """Main application window"""

    def __init__(self):
        super().__init__()
        self.video_thread = VideoThread()
        self.load_config()
        self.init_ui()
        self.setup_connections()
        self.apply_styles()
        
        # 添加响应式设计
        self.setup_responsive_design()

    def load_config(self):
        """Load configuration from settings.yaml"""
        try:
            with open('../settings.yaml', 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        except FileNotFoundError:
            # Use default config if file not found
            self.config = {
                'system': {
                    'pixel_to_meter': None
                }
            }

        # Get pixel_to_meter setting
        self.pixel_to_meter = self.config.get('system', {}).get('pixel_to_meter', None)
        
    def init_ui(self):
        """Initialize user interface"""
        self.setWindowTitle("Gait Detection System")
        self.setGeometry(100, 100, 1200, 800)
        self.setMinimumSize(1000, 600)  # 减少最小窗口尺寸
        
        # Central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        main_layout.setSpacing(10)  # 减少主布局间距
        main_layout.setContentsMargins(10, 10, 10, 10)  # 减少主布局边距
        
        # Left side - Video display
        self.setup_video_panel(main_layout)
        
        # Right side - Control panel
        self.setup_control_panel(main_layout)
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready - Click to start camera")
        
    def setup_video_panel(self, parent_layout):
        """Setup video display panel"""
        video_frame = QFrame()
        video_frame.setFrameStyle(QFrame.Box)
        video_frame.setLineWidth(2)
        video_frame.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        video_layout = QVBoxLayout(video_frame)
        video_layout.setContentsMargins(10, 10, 10, 10)  # 调整边距，移除标题后增加上边距

        # Video display label - 移除标题，直接显示视频区域
        self.video_label = QLabel()
        self.video_label.setAlignment(Qt.AlignCenter)
        self.video_label.setMinimumSize(640, 480)
        self.video_label.setStyleSheet("background-color: #2C3E50; color: white; font-size: 16px;")
        self.video_label.setText("Click to start camera")
        self.video_label.mousePressEvent = self.toggle_camera
        video_layout.addWidget(self.video_label)

        parent_layout.addWidget(video_frame, 2)  # 2/3 of the width

    def setup_control_panel(self, parent_layout):
        """Setup control panel with progress bars and status"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.Box)
        control_frame.setLineWidth(2)
        control_frame.setMaximumWidth(400)  # 减少宽度
        control_frame.setMinimumWidth(350)  # 减少最小宽度
        control_frame.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)

        control_layout = QVBoxLayout(control_frame)
        control_layout.setSpacing(8)  # 减少section之间的间距
        control_layout.setContentsMargins(8, 8, 8, 8)  # 减少邊距

        # Title
        title = QLabel("Gait Analysis")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 14, QFont.Bold))  # 减小标题字体
        title.setMinimumHeight(25)  # 减少最小高度
        control_layout.addWidget(title)

        # Overall status
        self.setup_overall_status(control_layout)

        # Auto scoring control
        self.setup_auto_scoring_control(control_layout)

        # Progress bars
        self.setup_progress_bars(control_layout)

        # Joint angles display
        self.setup_angles_display(control_layout)

        # Speed display
        self.setup_speed_display(control_layout)

        # Stretch to push everything to top
        control_layout.addStretch()

        parent_layout.addWidget(control_frame, 1)  # 1/3 of the width

    def setup_overall_status(self, parent_layout):
        """Setup overall gait status display"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        # 移除高度限制，让内容自然扩展
        status_layout = QVBoxLayout(status_frame)
        status_layout.setContentsMargins(8, 6, 8, 6)  # 减少邊距
        status_layout.setSpacing(4)  # 减少間距

        # Status label
        status_title = QLabel("Gait Status")
        status_title.setFont(QFont("Arial", 10, QFont.Bold))  # 减小字體大小
        status_title.setAlignment(Qt.AlignCenter)
        status_title.setMinimumHeight(18)  # 减少最小高度
        status_layout.addWidget(status_title)

        self.status_label = QLabel("No Person Detected")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setFont(QFont("Arial", 11, QFont.Bold))  # 减小字體大小
        self.status_label.setStyleSheet("color: #E74C3C; padding: 6px;")  # 减少padding
        self.status_label.setMinimumHeight(25)  # 减少最小高度
        status_layout.addWidget(self.status_label)

        parent_layout.addWidget(status_frame)

    def setup_auto_scoring_control(self, parent_layout):
        """Setup auto scoring control panel"""
        auto_scoring_frame = QFrame()
        auto_scoring_frame.setFrameStyle(QFrame.StyledPanel)
        # 移除高度限制，让内容自然扩展
        auto_scoring_layout = QVBoxLayout(auto_scoring_frame)
        auto_scoring_layout.setContentsMargins(8, 5, 8, 5)  # 减少邊距
        auto_scoring_layout.setSpacing(3)  # 减少間距

        # Title
        auto_scoring_title = QLabel("Auto Scoring")
        auto_scoring_title.setFont(QFont("Arial", 10, QFont.Bold))  # 减小字體大小
        auto_scoring_title.setAlignment(Qt.AlignCenter)
        auto_scoring_title.setMinimumHeight(16)  # 减少最小高度
        auto_scoring_layout.addWidget(auto_scoring_title)

        # Checkbox for enabling auto scoring
        self.auto_scoring_checkbox = QCheckBox("Enable Auto Scoring")
        self.auto_scoring_checkbox.setFont(QFont("Arial", 9))  # 减小字體大小
        self.auto_scoring_checkbox.setMinimumHeight(24)  # 减少最小高度
        self.auto_scoring_checkbox.setStyleSheet("""
            QCheckBox {
                color: #2C3E50;
                spacing: 6px;
                padding: 2px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #BDC3C7;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #3498DB;
                background-color: #3498DB;
                border-radius: 3px;
            }
        """)
        self.auto_scoring_checkbox.stateChanged.connect(self.on_auto_scoring_toggled)
        auto_scoring_layout.addWidget(self.auto_scoring_checkbox)

        # Status label for auto scoring
        self.auto_scoring_status = QLabel("Disabled")
        self.auto_scoring_status.setAlignment(Qt.AlignCenter)
        self.auto_scoring_status.setFont(QFont("Arial", 8))  # 减小字體大小
        self.auto_scoring_status.setStyleSheet("color: #7F8C8D; padding: 2px;")  # 减少padding
        self.auto_scoring_status.setMinimumHeight(16)  # 减少最小高度
        auto_scoring_layout.addWidget(self.auto_scoring_status)

        parent_layout.addWidget(auto_scoring_frame)

    def setup_progress_bars(self, parent_layout):
        """Setup angle and speed progress bars"""
        progress_frame = QFrame()
        progress_frame.setFrameStyle(QFrame.StyledPanel)
        # 移除高度限制，让内容自然扩展
        progress_layout = QVBoxLayout(progress_frame)
        progress_layout.setContentsMargins(8, 6, 8, 6)  # 减少邊距
        progress_layout.setSpacing(6)  # 减少間距

        # Angle Score Progress Bar Section
        angle_section_frame = QFrame()
        angle_section_frame.setFrameStyle(QFrame.StyledPanel)
        angle_section_layout = QVBoxLayout(angle_section_frame)
        angle_section_layout.setContentsMargins(6, 4, 6, 4)  # 减少section frame边距
        angle_section_layout.setSpacing(4)  # 减少间距

        angle_label = QLabel("Joint Angle Quality")
        angle_label.setFont(QFont("Arial", 9, QFont.Bold))  # 减小字体大小
        angle_label.setStyleSheet("""
            QLabel {
                color: #2C3E50;
                background-color: transparent;
                padding: 2px;
                border: none;
                margin: 0px;
            }
        """)
        angle_label.setToolTip("Evaluates knee joint angle quality during walking")
        angle_label.setMinimumHeight(16)  # 减少最小高度
        angle_section_layout.addWidget(angle_label)

        self.angle_progress = GradientProgressBar()
        self.angle_progress.setMinimum(0)
        self.angle_progress.setMaximum(100)
        self.angle_progress.setValue(0)
        self.angle_progress.setMinimumHeight(20)  # 减少進度條高度
        self.angle_progress.setMaximumHeight(20)  # 限制最大高度
        angle_section_layout.addWidget(self.angle_progress)

        self.angle_value_label = QLabel("0 / 100")
        self.angle_value_label.setAlignment(Qt.AlignCenter)
        self.angle_value_label.setFont(QFont("Arial", 9))  # 减小字体大小
        self.angle_value_label.setStyleSheet("color: #2C3E50; padding: 2px;")  # 减少padding
        self.angle_value_label.setMinimumHeight(16)  # 减少最小高度
        angle_section_layout.addWidget(self.angle_value_label)

        progress_layout.addWidget(angle_section_frame)
        progress_layout.addSpacing(4)  # 减少兩個進度條之間的間距

        # Speed Score Progress Bar Section
        speed_section_frame = QFrame()
        speed_section_frame.setFrameStyle(QFrame.StyledPanel)
        speed_section_layout = QVBoxLayout(speed_section_frame)
        speed_section_layout.setContentsMargins(6, 4, 6, 4)  # 减少section frame边距
        speed_section_layout.setSpacing(4)  # 减少间距

        speed_label = QLabel("Walking Speed Quality")
        speed_label.setFont(QFont("Arial", 9, QFont.Bold))  # 减小字体大小
        speed_label.setStyleSheet("""
            QLabel {
                color: #2C3E50;
                background-color: transparent;
                padding: 2px;
                border: none;
                margin: 0px;
            }
        """)
        speed_label.setToolTip("Evaluates walking speed quality and consistency")
        speed_label.setMinimumHeight(16)  # 减少最小高度
        speed_section_layout.addWidget(speed_label)

        self.speed_progress = GradientProgressBar()
        self.speed_progress.setMinimum(0)
        self.speed_progress.setMaximum(100)
        self.speed_progress.setValue(0)
        self.speed_progress.setMinimumHeight(20)  # 减少進度條高度
        self.speed_progress.setMaximumHeight(20)  # 限制最大高度
        speed_section_layout.addWidget(self.speed_progress)

        self.speed_value_label = QLabel("0 / 100")
        self.speed_value_label.setAlignment(Qt.AlignCenter)
        self.speed_value_label.setFont(QFont("Arial", 9))  # 减小字体大小
        self.speed_value_label.setStyleSheet("color: #2C3E50; padding: 2px;")  # 减少padding
        self.speed_value_label.setMinimumHeight(16)  # 减少最小高度
        speed_section_layout.addWidget(self.speed_value_label)

        progress_layout.addWidget(speed_section_frame)
        progress_layout.addSpacing(4)  # 减少兩個進度條之間的間距

        # Shoulder Balance Score Progress Bar Section
        shoulder_section_frame = QFrame()
        shoulder_section_frame.setFrameStyle(QFrame.StyledPanel)
        shoulder_section_layout = QVBoxLayout(shoulder_section_frame)
        shoulder_section_layout.setContentsMargins(6, 4, 6, 4)  # 减少section frame边距
        shoulder_section_layout.setSpacing(4)  # 减少间距

        shoulder_label = QLabel("Shoulder Balance")
        shoulder_label.setFont(QFont("Arial", 9, QFont.Bold))  # 减小字体大小
        shoulder_label.setStyleSheet("""
            QLabel {
                color: #2C3E50;
                background-color: transparent;
                padding: 2px;
                border: none;
                margin: 0px;
            }
        """)
        shoulder_label.setToolTip("Evaluates left-right shoulder balance during walking")
        shoulder_label.setMinimumHeight(16)  # 减少最小高度
        shoulder_section_layout.addWidget(shoulder_label)

        self.shoulder_balance_progress = GradientProgressBar()
        self.shoulder_balance_progress.setMinimum(0)
        self.shoulder_balance_progress.setMaximum(100)
        self.shoulder_balance_progress.setValue(0)
        self.shoulder_balance_progress.setMinimumHeight(20)  # 减少進度條高度
        self.shoulder_balance_progress.setMaximumHeight(20)  # 限制最大高度
        shoulder_section_layout.addWidget(self.shoulder_balance_progress)

        self.shoulder_balance_value_label = QLabel("0 / 100")
        self.shoulder_balance_value_label.setAlignment(Qt.AlignCenter)
        self.shoulder_balance_value_label.setFont(QFont("Arial", 9))  # 减小字体大小
        self.shoulder_balance_value_label.setStyleSheet("color: #2C3E50; padding: 2px;")  # 减少padding
        self.shoulder_balance_value_label.setMinimumHeight(16)  # 减少最小高度
        shoulder_section_layout.addWidget(self.shoulder_balance_value_label)

        progress_layout.addWidget(shoulder_section_frame)
        progress_layout.addSpacing(4)  # 减少兩個進度條之間的間距

        # Hunchback Detection Score Progress Bar Section
        hunchback_section_frame = QFrame()
        hunchback_section_frame.setFrameStyle(QFrame.StyledPanel)
        hunchback_section_layout = QVBoxLayout(hunchback_section_frame)
        hunchback_section_layout.setContentsMargins(6, 4, 6, 4)  # 减少section frame边距
        hunchback_section_layout.setSpacing(4)  # 减少间距

        hunchback_label = QLabel("Hunchback Detection")
        hunchback_label.setFont(QFont("Arial", 9, QFont.Bold))  # 减小字体大小
        hunchback_label.setStyleSheet("""
            QLabel {
                color: #2C3E50;
                background-color: transparent;
                padding: 2px;
                border: none;
                margin: 0px;
            }
        """)
        hunchback_label.setToolTip("Evaluates posture and detects forward head posture")
        hunchback_label.setMinimumHeight(16)  # 减少最小高度
        hunchback_section_layout.addWidget(hunchback_label)

        self.hunchback_progress = GradientProgressBar()
        self.hunchback_progress.setMinimum(0)
        self.hunchback_progress.setMaximum(100)
        self.hunchback_progress.setValue(0)
        self.hunchback_progress.setMinimumHeight(20)  # 减少進度條高度
        self.hunchback_progress.setMaximumHeight(20)  # 限制最大高度
        hunchback_section_layout.addWidget(self.hunchback_progress)

        self.hunchback_value_label = QLabel("0 / 100")
        self.hunchback_value_label.setAlignment(Qt.AlignCenter)
        self.hunchback_value_label.setFont(QFont("Arial", 9))  # 减小字体大小
        self.hunchback_value_label.setStyleSheet("color: #2C3E50; padding: 2px;")  # 减少padding
        self.hunchback_value_label.setMinimumHeight(16)  # 减少最小高度
        hunchback_section_layout.addWidget(self.hunchback_value_label)

        progress_layout.addWidget(hunchback_section_frame)
        parent_layout.addWidget(progress_frame)

    def setup_angles_display(self, parent_layout):
        """Setup joint angles display"""
        angles_frame = QFrame()
        angles_frame.setFrameStyle(QFrame.StyledPanel)
        # 移除高度限制，让内容自然扩展
        angles_layout = QVBoxLayout(angles_frame)
        angles_layout.setContentsMargins(8, 4, 8, 4)  # 减少邊距
        angles_layout.setSpacing(3)  # 减少間距

        angles_title = QLabel("Joint Angles")
        angles_title.setFont(QFont("Arial", 9, QFont.Bold))  # 减小字體大小
        angles_title.setAlignment(Qt.AlignCenter)
        angles_title.setMinimumHeight(16)  # 减少最小高度
        angles_layout.addWidget(angles_title)

        # Create labels for angles
        self.left_knee_label = QLabel("Left Knee: 0.0°")
        self.left_knee_label.setFont(QFont("Arial", 8))  # 减小字體大小
        self.left_knee_label.setStyleSheet("color: #2C3E50; padding: 1px;")  # 减少padding
        self.left_knee_label.setMinimumHeight(14)  # 减少最小高度
        angles_layout.addWidget(self.left_knee_label)

        self.right_knee_label = QLabel("Right Knee: 0.0°")
        self.right_knee_label.setFont(QFont("Arial", 8))  # 减小字體大小
        self.right_knee_label.setStyleSheet("color: #2C3E50; padding: 1px;")  # 减少padding
        self.right_knee_label.setMinimumHeight(14)  # 减少最小高度
        angles_layout.addWidget(self.right_knee_label)

        parent_layout.addWidget(angles_frame)

    def setup_speed_display(self, parent_layout):
        """Setup speed display"""
        speed_frame = QFrame()
        speed_frame.setFrameStyle(QFrame.StyledPanel)
        # 移除高度限制，让内容自然扩展
        speed_layout = QVBoxLayout(speed_frame)
        speed_layout.setContentsMargins(8, 4, 8, 4)  # 减少邊距
        speed_layout.setSpacing(3)  # 减少間距

        speed_title = QLabel("Walking Speed")
        speed_title.setFont(QFont("Arial", 9, QFont.Bold))  # 减小字體大小
        speed_title.setAlignment(Qt.AlignCenter)
        speed_title.setMinimumHeight(16)  # 减少最小高度
        speed_layout.addWidget(speed_title)

        self.speed_display_label = QLabel("0.00 pixels/s")
        self.speed_display_label.setAlignment(Qt.AlignCenter)
        self.speed_display_label.setFont(QFont("Arial", 10, QFont.Bold))  # 减小字體大小
        self.speed_display_label.setStyleSheet("color: #2C3E50; padding: 2px;")  # 减少padding
        self.speed_display_label.setMinimumHeight(18)  # 减少最小高度
        speed_layout.addWidget(self.speed_display_label)

        parent_layout.addWidget(speed_frame)

    def setup_connections(self):
        """Setup signal connections"""
        self.video_thread.frame_ready.connect(self.update_video_display)
        self.video_thread.gait_data_ready.connect(self.update_gait_data)
        self.video_thread.auto_scoring_result.connect(self.show_auto_scoring_result)
        self.video_thread.dialog_closed.connect(self.video_thread.on_dialog_closed)

    def toggle_camera(self, event=None):
        """Toggle camera on/off"""
        # event parameter is used for mouse click event handling
        if not self.video_thread.running:
            self.video_thread.start_capture()
            self.status_bar.showMessage("Camera started")
            self.video_label.setText("Loading camera...")
        else:
            self.video_thread.stop_capture()
            self.status_bar.showMessage("Camera stopped")
            self.video_label.setText("Click to start camera")
            self.reset_displays()

    def update_video_display(self, frame):
        """Update video display with new frame"""
        # Draw pose landmarks on frame if available
        if hasattr(self, 'current_pose_landmarks') and self.current_pose_landmarks is not None:
            frame = self.draw_pose_landmarks(frame, self.current_pose_landmarks)

        # Convert frame to QImage
        rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        h, w, ch = rgb_image.shape
        bytes_per_line = ch * w
        qt_image = QImage(rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888)

        # Scale image to fit label while maintaining aspect ratio
        pixmap = QPixmap.fromImage(qt_image)
        scaled_pixmap = pixmap.scaled(self.video_label.size(), Qt.KeepAspectRatio, Qt.SmoothTransformation)
        self.video_label.setPixmap(scaled_pixmap)

    def draw_pose_landmarks(self, frame, pose_landmarks):
        """Draw pose landmarks on frame"""
        import mediapipe as mp
        mp_drawing = mp.solutions.drawing_utils
        mp_pose = mp.solutions.pose

        mp_drawing.draw_landmarks(
            frame,
            pose_landmarks,
            mp_pose.POSE_CONNECTIONS,
            mp_drawing.DrawingSpec(color=(0, 255, 0), thickness=2, circle_radius=2),
            mp_drawing.DrawingSpec(color=(0, 0, 255), thickness=2)
        )
        return frame

    def update_gait_data(self, gait_data):
        """Update all gait-related displays"""
        self.current_pose_landmarks = gait_data['pose_landmarks']

        if gait_data['has_person']:
            # Update overall status
            level = gait_data['level']
            reason = gait_data['reason']

            level_colors = {
                'excellent': '#27AE60',  # Green
                'good': '#F39C12',       # Orange
                'poor': '#E74C3C'        # Red
            }

            level_text = {
                'excellent': 'Excellent Gait',
                'good': 'Good Gait',
                'poor': 'Poor Gait'
            }

            status_text = level_text.get(level, 'Unknown')
            if reason and level != 'excellent':
                status_text += f" ({reason})"

            self.status_label.setText(status_text)
            self.status_label.setStyleSheet(f"color: {level_colors.get(level, '#E74C3C')}; padding: 10px;")

            # Update progress bars
            angle_score = int(gait_data['angle_score'])
            speed_score = int(gait_data['speed_score'])
            shoulder_balance_score = int(gait_data['shoulder_balance_score'])
            hunchback_score = int(gait_data['hunchback_score'])

            self.angle_progress.setValue(angle_score)
            self.angle_value_label.setText(f"{angle_score} / 100")

            self.speed_progress.setValue(speed_score)
            self.speed_value_label.setText(f"{speed_score} / 100")

            self.shoulder_balance_progress.setValue(shoulder_balance_score)
            self.shoulder_balance_value_label.setText(f"{shoulder_balance_score} / 100")

            self.hunchback_progress.setValue(hunchback_score)
            self.hunchback_value_label.setText(f"{hunchback_score} / 100")

            # Update joint angles
            angles = gait_data['angles']
            left_knee = angles.get('left_knee', 0)
            right_knee = angles.get('right_knee', 0)

            self.left_knee_label.setText(f"Left Knee: {left_knee:.1f}°")
            self.right_knee_label.setText(f"Right Knee: {right_knee:.1f}°")

            # Update speed - 根據pixel_to_meter設定決定顯示單位
            speed = gait_data['speed']
            if self.pixel_to_meter is not None:
                # 有實際標定，顯示公尺/秒
                self.speed_display_label.setText(f"{speed:.2f} m/s")
            else:
                # 沒有標定，顯示像素/秒
                self.speed_display_label.setText(f"{speed:.2f} pixels/s")

            self.status_bar.showMessage(f"Person detected - {status_text}")

        else:
            # No person detected
            self.reset_displays()
            self.status_bar.showMessage("No person detected - Please start walking")

    def reset_displays(self):
        """Reset all displays to default values"""
        self.status_label.setText("No Person Detected")
        self.status_label.setStyleSheet("color: #E74C3C; padding: 10px;")

        self.angle_progress.setValue(0)
        self.angle_value_label.setText("0 / 100")

        self.speed_progress.setValue(0)
        self.speed_value_label.setText("0 / 100")

        self.shoulder_balance_progress.setValue(0)
        self.shoulder_balance_value_label.setText("0 / 100")

        self.hunchback_progress.setValue(0)
        self.hunchback_value_label.setText("0 / 100")

        self.left_knee_label.setText("Left Knee: 0.0°")
        self.right_knee_label.setText("Right Knee: 0.0°")

        # 根據pixel_to_meter設定決定顯示單位
        if self.pixel_to_meter is not None:
            self.speed_display_label.setText("0.00 m/s")
        else:
            self.speed_display_label.setText("0.00 pixels/s")

    def apply_styles(self):
        """Apply custom styles to the application"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #ECF0F1;
            }
            QFrame {
                background-color: white;
                border-radius: 8px;
                padding: 10px;
            }
            QLabel {
                color: #2C3E50;
            }
            QStatusBar {
                background-color: #34495E;
                color: white;
                font-weight: bold;
            }
        """)

    def closeEvent(self, event):
        """Handle application close event"""
        if self.video_thread.running:
            self.video_thread.stop_capture()
        event.accept()

    def on_auto_scoring_toggled(self, state):
        """Handle auto scoring checkbox toggle"""
        # Handle both integer and Qt.CheckState values
        if isinstance(state, int):
            enabled = state == Qt.Checked.value
        else:
            enabled = state == Qt.Checked

        self.video_thread.set_auto_scoring_enabled(enabled)

        if enabled:
            self.auto_scoring_status.setText("Waiting for person...")
            self.auto_scoring_status.setStyleSheet("color: #F39C12; padding: 2px;")
        else:
            self.auto_scoring_status.setText("Disabled")
            self.auto_scoring_status.setStyleSheet("color: #7F8C8D; padding: 2px;")

        # 強制更新UI
        self.auto_scoring_status.update()

    def show_auto_scoring_result(self, result):
        """Show auto scoring result in a dialog"""
        dialog = AutoScoringResultDialog(result, self)
        dialog.finished.connect(lambda: self.video_thread.dialog_closed.emit())
        dialog.exec()

    def setup_responsive_design(self):
        """Setup responsive design for window resizing"""
        # 存储初始窗口尺寸
        self.window_width = self.width()
        self.window_height = self.height()
        
        # 存储需要动态调整的控件引用
        self.responsive_widgets = {}

    def on_resize(self, event):
        """Handle window resize event"""
        new_width = self.width()
        new_height = self.height()
        
        # 根据窗口大小调整字体
        self.adjust_fonts_for_size(new_width, new_height)
        
        # 调用原始的resizeEvent
        super().resizeEvent(event)

    def adjust_fonts_for_size(self, width, height):
        """Adjust font sizes based on window dimensions"""
        # 小窗口：使用较小字体
        if width < 1000 or height < 600:
            self.adjust_fonts_small()
        # 中等窗口：使用中等字体
        elif width < 1200 or height < 700:
            self.adjust_fonts_medium()
        # 大窗口：使用较大字体
        else:
            self.adjust_fonts_large()

    def adjust_fonts_small(self):
        """Adjust fonts for small window size"""
        # 调整标题字体
        if hasattr(self, 'status_label'):
            self.status_label.setFont(QFont("Arial", 9, QFont.Bold))
        if hasattr(self, 'auto_scoring_checkbox'):
            self.auto_scoring_checkbox.setFont(QFont("Arial", 8))
        if hasattr(self, 'auto_scoring_status'):
            self.auto_scoring_status.setFont(QFont("Arial", 7))
        if hasattr(self, 'angle_value_label'):
            self.angle_value_label.setFont(QFont("Arial", 8))
        if hasattr(self, 'speed_value_label'):
            self.speed_value_label.setFont(QFont("Arial", 8))
        if hasattr(self, 'shoulder_balance_value_label'):
            self.shoulder_balance_value_label.setFont(QFont("Arial", 8))
        if hasattr(self, 'hunchback_value_label'):
            self.hunchback_value_label.setFont(QFont("Arial", 8))
        if hasattr(self, 'left_knee_label'):
            self.left_knee_label.setFont(QFont("Arial", 7))
        if hasattr(self, 'right_knee_label'):
            self.right_knee_label.setFont(QFont("Arial", 7))
        if hasattr(self, 'speed_display_label'):
            self.speed_display_label.setFont(QFont("Arial", 9, QFont.Bold))

    def adjust_fonts_medium(self):
        """Adjust fonts for medium window size"""
        if hasattr(self, 'status_label'):
            self.status_label.setFont(QFont("Arial", 11, QFont.Bold))
        if hasattr(self, 'auto_scoring_checkbox'):
            self.auto_scoring_checkbox.setFont(QFont("Arial", 9))
        if hasattr(self, 'auto_scoring_status'):
            self.auto_scoring_status.setFont(QFont("Arial", 8))
        if hasattr(self, 'angle_value_label'):
            self.angle_value_label.setFont(QFont("Arial", 9))
        if hasattr(self, 'speed_value_label'):
            self.speed_value_label.setFont(QFont("Arial", 9))
        if hasattr(self, 'shoulder_balance_value_label'):
            self.shoulder_balance_value_label.setFont(QFont("Arial", 9))
        if hasattr(self, 'hunchback_value_label'):
            self.hunchback_value_label.setFont(QFont("Arial", 9))
        if hasattr(self, 'left_knee_label'):
            self.left_knee_label.setFont(QFont("Arial", 8))
        if hasattr(self, 'right_knee_label'):
            self.right_knee_label.setFont(QFont("Arial", 8))
        if hasattr(self, 'speed_display_label'):
            self.speed_display_label.setFont(QFont("Arial", 10, QFont.Bold))

    def adjust_fonts_large(self):
        """Adjust fonts for large window size"""
        if hasattr(self, 'status_label'):
            self.status_label.setFont(QFont("Arial", 13, QFont.Bold))
        if hasattr(self, 'auto_scoring_checkbox'):
            self.auto_scoring_checkbox.setFont(QFont("Arial", 11))
        if hasattr(self, 'auto_scoring_status'):
            self.auto_scoring_status.setFont(QFont("Arial", 10))
        if hasattr(self, 'angle_value_label'):
            self.angle_value_label.setFont(QFont("Arial", 11))
        if hasattr(self, 'speed_value_label'):
            self.speed_value_label.setFont(QFont("Arial", 11))
        if hasattr(self, 'shoulder_balance_value_label'):
            self.shoulder_balance_value_label.setFont(QFont("Arial", 11))
        if hasattr(self, 'hunchback_value_label'):
            self.hunchback_value_label.setFont(QFont("Arial", 11))
        if hasattr(self, 'left_knee_label'):
            self.left_knee_label.setFont(QFont("Arial", 10))
        if hasattr(self, 'right_knee_label'):
            self.right_knee_label.setFont(QFont("Arial", 10))
        if hasattr(self, 'speed_display_label'):
            self.speed_display_label.setFont(QFont("Arial", 12, QFont.Bold))

    def resizeEvent(self, event):
        """Override resizeEvent to handle responsive design"""
        self.on_resize(event)


class AutoScoringResultDialog(QDialog):
    """Dialog to display auto scoring results"""

    def __init__(self, result, parent=None):
        super().__init__(parent)
        self.result = result
        self.countdown = 15
        self.init_ui()
        self.setup_timer()

    def init_ui(self):
        """Initialize the dialog UI"""
        self.setWindowTitle("Auto Scoring Result")
        self.setFixedSize(500, 450)  # 進一步增加對話框大小
        self.setModal(True)

        # Center the dialog on parent
        if self.parent():
            parent_geo = self.parent().geometry()
            x = parent_geo.x() + (parent_geo.width() - 500) // 2
            y = parent_geo.y() + (parent_geo.height() - 450) // 2
            self.move(x, y)

        # Set dialog-specific stylesheet to override main window styles
        self.setStyleSheet("""
            QDialog {
                background-color: #2C3E50;
                color: white;
            }
            QLabel {
                background-color: transparent;
                border: none;
                padding: 0px;
                margin: 0px;
                color: white;
            }
            QPushButton {
                background-color: #3498DB;
                color: white;
                border: none;
                padding: 8px 20px;
                border-radius: 4px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #2980B9;
            }
        """)

        # Create main layout
        layout = QVBoxLayout(self)
        layout.setSpacing(20)  # 增加間距確保元素不重疊
        layout.setContentsMargins(30, 30, 30, 30)  # 增加邊距

        # Title
        title = QLabel("Gait Analysis Complete")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setStyleSheet("color: #ECF0F1;")  # 淺色文字在深色背景上
        title.setMinimumHeight(30)
        title.setMaximumHeight(40)
        layout.addWidget(title)

        # Final score
        final_score = self.result['final_score']
        level = self.result['level']

        score_text = f"{final_score:.1f}"
        self.score_label = QLabel(score_text)
        self.score_label.setAlignment(Qt.AlignCenter)
        self.score_label.setFont(QFont("Arial", 36, QFont.Bold))
        self.score_label.setMinimumHeight(60)

        # Set color based on level
        if level == 'excellent':
            color = "#27AE60"
        elif level == 'good':
            color = "#F39C12"
        else:
            color = "#E74C3C"

        self.score_label.setStyleSheet(f"color: {color};")
        self.score_label.setMaximumHeight(80)  # 限制最大高度
        layout.addWidget(self.score_label)

        # Level label
        level_text = level.upper()
        self.level_label = QLabel(level_text)
        self.level_label.setAlignment(Qt.AlignCenter)
        self.level_label.setFont(QFont("Arial", 14, QFont.Bold))
        self.level_label.setStyleSheet(f"color: {color};")
        self.level_label.setMinimumHeight(25)
        self.level_label.setMaximumHeight(35)
        layout.addWidget(self.level_label)

        # Details
        details_text = (f"Average Angle Score: {self.result['avg_angle_score']:.1f}\n"
                       f"Average Speed Score: {self.result['avg_speed_score']:.1f}\n"
                       f"Average Shoulder Balance: {self.result['avg_shoulder_balance_score']:.1f}\n"
                       f"Average Hunchback Score: {self.result['avg_hunchback_score']:.1f}\n"
                       f"Session Duration: {self.result['session_duration']:.1f}s")

        details_label = QLabel(details_text)
        details_label.setAlignment(Qt.AlignCenter)
        details_label.setFont(QFont("Arial", 11))
        details_label.setStyleSheet("color: #BDC3C7;")  # 較淺的灰色在深色背景上
        details_label.setMinimumHeight(60)
        details_label.setMaximumHeight(80)
        details_label.setWordWrap(True)
        layout.addWidget(details_label)

        # Countdown label
        self.countdown_label = QLabel(f"Auto close in {self.countdown} seconds")
        self.countdown_label.setAlignment(Qt.AlignCenter)
        self.countdown_label.setFont(QFont("Arial", 10))
        self.countdown_label.setStyleSheet("color: #95A5A6;")  # 保持原色，在深色背景下仍可見
        self.countdown_label.setMinimumHeight(20)
        self.countdown_label.setMaximumHeight(25)
        layout.addWidget(self.countdown_label)

        # Close button
        close_button = QPushButton("Close Now")
        close_button.setFont(QFont("Arial", 11))
        close_button.setMinimumHeight(35)
        close_button.clicked.connect(self.accept)
        layout.addWidget(close_button)

    def setup_timer(self):
        """Setup countdown timer"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_countdown)
        self.timer.start(1000)  # Update every second

    def update_countdown(self):
        """Update countdown and close dialog when reaches 0"""
        self.countdown -= 1
        self.countdown_label.setText(f"Auto close in {self.countdown} seconds")

        if self.countdown <= 0:
            self.timer.stop()
            self.accept()


def main():
    """Main application entry point"""
    app = QApplication(sys.argv)
    app.setApplicationName("Gait Detection System")
    app.setOrganizationName("Gait Analysis Lab")

    # Set application style
    app.setStyle('Fusion')

    # Create and show main window
    window = MainWindow()
    window.show()

    # Run application
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
