import numpy as np
import math

class SideViewHunchbackDetector:
    """
    針對側面攝影機視角的駝背檢測演算法
    
    主要原理：
    1. 利用側面視角可以清楚看到脊椎彎曲的優勢
    2. 分析頭-頸-肩-背的角度關係
    3. 檢測前頭姿勢(Forward Head Posture)和圓肩(Rounded Shoulders)
    """
    
    def __init__(self):
        # MediaPipe 關鍵點索引
        self.NOSE = 0
        self.LEFT_EYE = 1
        self.RIGHT_EYE = 2
        self.LEFT_EAR = 7
        self.RIGHT_EAR = 8
        self.LEFT_SHOULDER = 11
        self.RIGHT_SHOULDER = 12
        self.LEFT_ELBOW = 13
        self.RIGHT_ELBOW = 14
        self.LEFT_HIP = 23
        self.RIGHT_HIP = 24
        
        # 評分閾值（角度，單位：度）
        self.thresholds = {
            'head_neck_angle': {
                'excellent': (45, 60),    # 正常範圍
                'good': (35, 70),         # 輕微異常
                'poor_min': 20,           # 嚴重前傾
                'poor_max': 80            # 過度後仰
            },
            'shoulder_alignment': {
                'excellent': (-5, 5),     # 肩膀水平對齊
                'good': (-10, 10),        # 輕微不對齊
                'poor_threshold': 15      # 明顯駝背
            },
            'cvt_angle': {  # Craniovertebral Angle (頭頸角)
                'excellent_min': 48,      # 正常CVT角度
                'good_min': 42,           # 輕微前頭姿勢
                'poor_max': 38            # 嚴重前頭姿勢
            }
        }
    
    def detect_hunchback(self, pose_landmarks):
        """
        檢測駝背程度
        
        Args:
            pose_landmarks: MediaPipe pose landmarks
            
        Returns:
            dict: 包含各項檢測結果和總分
        """
        if pose_landmarks is None:
            return {'score': 0, 'details': {}}
        
        try:
            lm = pose_landmarks.landmark
            
            # 1. 計算頭頸角度 (Head-Neck Angle)
            head_neck_angle = self._calculate_head_neck_angle(lm)
            
            # 2. 計算肩膀對齊度 (Shoulder Alignment)
            shoulder_alignment = self._calculate_shoulder_alignment(lm)
            
            # 3. 計算CVT角度 (Craniovertebral Angle)
            cvt_angle = self._calculate_cvt_angle(lm)
            
            # 4. 計算前頭姿勢程度 (Forward Head Posture)
            fhp_ratio = self._calculate_forward_head_posture(lm)
            
            # 5. 計算圓肩程度 (Rounded Shoulders)
            rounded_shoulder_angle = self._calculate_rounded_shoulders(lm)
            
            # 綜合評分
            scores = {
                'head_neck_angle': self._score_head_neck_angle(head_neck_angle),
                'shoulder_alignment': self._score_shoulder_alignment(shoulder_alignment),
                'cvt_angle': self._score_cvt_angle(cvt_angle),
                'forward_head_posture': self._score_forward_head_posture(fhp_ratio),
                'rounded_shoulders': self._score_rounded_shoulders(rounded_shoulder_angle)
            }
            
            # 加權平均計算總分
            weights = {
                'head_neck_angle': 0.3,
                'shoulder_alignment': 0.2,
                'cvt_angle': 0.25,
                'forward_head_posture': 0.15,
                'rounded_shoulders': 0.1
            }
            
            total_score = sum(scores[key] * weights[key] for key in scores)
            
            return {
                'score': int(total_score),
                'details': {
                    'head_neck_angle': head_neck_angle,
                    'shoulder_alignment': shoulder_alignment,
                    'cvt_angle': cvt_angle,
                    'forward_head_posture': fhp_ratio,
                    'rounded_shoulders': rounded_shoulder_angle,
                    'individual_scores': scores
                }
            }
            
        except (AttributeError, IndexError, ZeroDivisionError):
            return {'score': 0, 'details': {}}
    
    def _calculate_head_neck_angle(self, landmarks):
        """
        計算頭頸角度
        使用耳朵-肩膀-髖部的角度關係
        """
        # 使用較靠近攝影機的一側（假設是右側）
        ear = landmarks[self.RIGHT_EAR]
        shoulder = landmarks[self.RIGHT_SHOULDER]
        hip = landmarks[self.RIGHT_HIP]
        
        # 計算向量
        vec1 = np.array([ear.x - shoulder.x, ear.y - shoulder.y])
        vec2 = np.array([hip.x - shoulder.x, hip.y - shoulder.y])
        
        # 計算角度
        angle = self._calculate_angle_between_vectors(vec1, vec2)
        return angle
    
    def _calculate_shoulder_alignment(self, landmarks):
        """
        計算肩膀對齊度（前後傾斜）
        正值表示肩膀前傾，負值表示後傾
        """
        left_shoulder = landmarks[self.LEFT_SHOULDER]
        right_shoulder = landmarks[self.RIGHT_SHOULDER]
        
        # 計算肩膀連線與水平線的角度
        dx = right_shoulder.x - left_shoulder.x
        dy = right_shoulder.y - left_shoulder.y
        
        angle = math.degrees(math.atan2(dy, dx))
        return angle
    
    def _calculate_cvt_angle(self, landmarks):
        """
        計算頭頸角 (Craniovertebral Angle)
        這是評估前頭姿勢的標準指標
        """
        # 使用耳朵、肩膀頂點、第7頸椎點（用肩膀中心代替）
        ear = landmarks[self.RIGHT_EAR]
        shoulder = landmarks[self.RIGHT_SHOULDER]
        
        # 計算肩膀中心作為頸椎參考點
        left_shoulder = landmarks[self.LEFT_SHOULDER]
        neck_base_x = (shoulder.x + left_shoulder.x) / 2
        neck_base_y = (shoulder.y + left_shoulder.y) / 2
        
        # 計算水平線向量和耳朵-頸椎向量
        horizontal_vec = np.array([1, 0])
        ear_neck_vec = np.array([ear.x - neck_base_x, ear.y - neck_base_y])
        
        angle = self._calculate_angle_between_vectors(horizontal_vec, ear_neck_vec)
        return angle
    
    def _calculate_forward_head_posture(self, landmarks):
        """
        計算前頭姿勢比例
        測量頭部相對於身體軸線的前移程度
        """
        # 頭部位置（使用鼻子或耳朵）
        head = landmarks[self.NOSE]
        
        # 身體軸線（肩膀中心到髖部中心）
        left_shoulder = landmarks[self.LEFT_SHOULDER]
        right_shoulder = landmarks[self.RIGHT_SHOULDER]
        left_hip = landmarks[self.LEFT_HIP]
        right_hip = landmarks[self.RIGHT_HIP]
        
        shoulder_center_x = (left_shoulder.x + right_shoulder.x) / 2
        hip_center_x = (left_hip.x + right_hip.x) / 2
        
        # 理想頭部位置（在身體軸線上）
        ideal_head_x = shoulder_center_x
        
        # 計算前移比例
        forward_displacement = head.x - ideal_head_x
        body_width = abs(left_shoulder.x - right_shoulder.x)
        
        if body_width > 0:
            forward_ratio = forward_displacement / body_width
        else:
            forward_ratio = 0
            
        return forward_ratio
    
    def _calculate_rounded_shoulders(self, landmarks):
        """
        計算圓肩角度
        測量肩膀相對於背部的前傾程度
        """
        # 使用肩膀和肘部的位置關係
        shoulder = landmarks[self.RIGHT_SHOULDER]
        elbow = landmarks[self.RIGHT_ELBOW]
        hip = landmarks[self.RIGHT_HIP]
        
        # 計算肩膀-髖部的垂直線
        vertical_vec = np.array([0, hip.y - shoulder.y])
        shoulder_elbow_vec = np.array([elbow.x - shoulder.x, elbow.y - shoulder.y])
        
        angle = self._calculate_angle_between_vectors(vertical_vec, shoulder_elbow_vec)
        return angle
    
    def _calculate_angle_between_vectors(self, vec1, vec2):
        """計算兩個向量之間的角度（度）"""
        cos_angle = np.dot(vec1, vec2) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
        cos_angle = np.clip(cos_angle, -1.0, 1.0)
        angle_rad = np.arccos(cos_angle)
        return math.degrees(angle_rad)
    
    # 評分函數
    def _score_head_neck_angle(self, angle):
        """評分頭頸角度"""
        excellent_range = self.thresholds['head_neck_angle']['excellent']
        good_range = self.thresholds['head_neck_angle']['good']
        
        if excellent_range[0] <= angle <= excellent_range[1]:
            return 100
        elif good_range[0] <= angle <= good_range[1]:
            return 75
        else:
            return 40
    
    def _score_shoulder_alignment(self, angle):
        """評分肩膀對齊度"""
        excellent_range = self.thresholds['shoulder_alignment']['excellent']
        good_range = self.thresholds['shoulder_alignment']['good']
        
        abs_angle = abs(angle)
        if abs_angle <= max(abs(excellent_range[0]), abs(excellent_range[1])):
            return 100
        elif abs_angle <= max(abs(good_range[0]), abs(good_range[1])):
            return 75
        else:
            return 40
    
    def _score_cvt_angle(self, angle):
        """評分CVT角度"""
        if angle >= self.thresholds['cvt_angle']['excellent_min']:
            return 100
        elif angle >= self.thresholds['cvt_angle']['good_min']:
            return 75
        else:
            return 40
    
    def _score_forward_head_posture(self, ratio):
        """評分前頭姿勢"""
        abs_ratio = abs(ratio)
        if abs_ratio <= 0.1:
            return 100
        elif abs_ratio <= 0.2:
            return 75
        else:
            return 40
    
    def _score_rounded_shoulders(self, angle):
        """評分圓肩程度"""
        if 80 <= angle <= 100:  # 接近垂直
            return 100
        elif 70 <= angle <= 110:
            return 75
        else:
            return 40


# 使用示例
def example_usage():
    """使用示例"""
    detector = SideViewHunchbackDetector()
    
    # 假設已有 pose_landmarks
    # result = detector.detect_hunchback(pose_landmarks)
    # print(f"駝背評分: {result['score']}")
    # print(f"詳細資訊: {result['details']}")
    
    print("側面駝背檢測演算法已就緒")
    print("主要檢測指標:")
    print("1. 頭頸角度 (Head-Neck Angle)")
    print("2. 肩膀對齊度 (Shoulder Alignment)")
    print("3. 頭頸角 (Craniovertebral Angle)")
    print("4. 前頭姿勢 (Forward Head Posture)")
    print("5. 圓肩程度 (Rounded Shoulders)")

if __name__ == "__main__":
    example_usage() 