# 側面駝背檢測系統使用指南

## 概述

本系統採用專為側面攝影機視角設計的駝背檢測演算法，能夠精確分析人體姿勢，檢測前頭姿勢、圓肩等常見姿勢問題。

## 系統特點

### 1. 醫學標準參考
- 基於醫學研究的CVT角度（Craniovertebral Angle）評估
- 參考物理治療和復健醫學的姿勢評估標準
- 提供量化的姿勢分析結果

### 2. 多指標綜合分析
- **頭頸角度**：評估頭部與頸部的角度關係
- **CVT角度**：標準醫學指標，評估前頭姿勢
- **前頭姿勢程度**：測量頭部前移的程度
- **圓肩程度**：檢測肩膀前傾的情況

### 3. 側面視角專用
- 專為側面攝影機設計，充分利用側面視角的優勢
- 能夠清楚觀察到脊椎彎曲和頭部前傾
- 避免正面視角的檢測盲點

## 攝影機設置

### 推薦設置
```
位置：人體側面（左側或右側）
高度：與肩膀同高或略高（約1.2-1.5公尺）
距離：2-3公尺，確保完整拍攝頭部到髖部
角度：垂直於人體行走方向
```

### 環境要求
- 充足的光線，確保關鍵點檢測準確
- 背景簡潔，避免干擾
- 足夠的空間讓受測者自然行走

## 檢測指標詳解

### 1. 頭頸角度 (Head-Neck Angle)
**計算方法**：耳朵-肩膀-髖部三點形成的角度
**正常範圍**：45°-60°
**評分權重**：35%

**評分標準**：
- 優良：45°-60°（100分）
- 一般：35°-70°（75分）
- 不佳：<35° 或 >70°（40分）

### 2. CVT角度 (Craniovertebral Angle)
**計算方法**：耳朵到頸椎基部與水平線的夾角
**正常範圍**：≥48°
**評分權重**：30%

**評分標準**：
- 優良：≥48°（100分）
- 一般：42°-48°（75分）
- 不佳：<42°（40分）

### 3. 前頭姿勢程度 (Forward Head Posture)
**計算方法**：頭部前移距離與身體寬度的比例
**正常範圍**：<10%
**評分權重**：25%

**評分標準**：
- 優良：<10%（100分）
- 一般：10%-20%（75分）
- 不佳：>20%（40分）

### 4. 圓肩程度 (Rounded Shoulders)
**計算方法**：肩膀-肘部向量與垂直線的夾角
**正常範圍**：80°-100°
**評分權重**：10%

**評分標準**：
- 優良：80°-100°（100分）
- 一般：70°-110°（75分）
- 不佳：<70° 或 >110°（40分）

## 配置參數

### settings.yaml 配置
```yaml
gait_analysis:
  hunchback_thresholds:
    # 頭頸角度閾值（度）
    head_neck_angle:
      excellent_range: [45, 60]
      good_range: [35, 70]
      poor_min: 20
      poor_max: 80
    
    # CVT角度閾值（度）
    cvt_angle:
      excellent_min: 48
      good_min: 42
      poor_max: 38
    
    # 前頭姿勢閾值（比例）
    forward_head_posture:
      excellent_max: 0.1
      good_max: 0.2
    
    # 圓肩角度閾值（度）
    rounded_shoulders:
      excellent_range: [80, 100]
      good_range: [70, 110]
```

## 使用建議

### 1. 測試前準備
- 確保攝影機位置正確
- 檢查光線是否充足
- 讓受測者熟悉測試環境

### 2. 測試過程
- 受測者自然行走，不要刻意改變姿勢
- 保持側面對著攝影機
- 行走距離建議3-5公尺

### 3. 結果解讀
- **85-100分**：姿勢優良，無明顯問題
- **65-85分**：輕微問題，建議注意姿勢
- **20-65分**：明顯問題，建議尋求專業建議

## 常見問題

### Q: 為什麼要使用側面視角？
A: 側面視角能夠清楚觀察到頭部前傾、脊椎彎曲等姿勢問題，這些在正面視角下很難準確檢測。

### Q: 檢測結果的準確性如何？
A: 系統基於醫學標準設計，但僅供參考。如有嚴重姿勢問題，建議諮詢專業醫療人員。

### Q: 可以調整檢測參數嗎？
A: 可以通過修改 settings.yaml 文件來調整各項閾值，以適應不同的評估標準。

### Q: 左側和右側檢測有差異嗎？
A: 系統預設使用右側檢測，但可以根據需要調整。建議選擇能夠清楚觀察到受測者的一側。

## 技術支援

如有技術問題或需要進一步的配置協助，請參考：
- README.md：系統整體說明
- settings.yaml：配置參數詳解
- 原始碼註解：詳細的演算法說明
