# Demo模式使用指南

## 概述

Demo模式是為了展示和測試目的而設計的功能，可以模擬進度條數值的動態變化，讓您在沒有真實攝影機輸入的情況下也能看到系統的運作效果。

## 功能特點

### 1. 獨立控制
- 四個指標（關節角度、行走速度、肩膀平衡、駝背檢測）可以獨立開啟/關閉Demo模式
- 未開啟Demo模式的指標會繼續使用真實的演算法計算

### 2. 真實模擬
- 數值會像真實數據一樣不斷跳動變化
- 支援週期性變化模式，模擬真實的生理數據
- 可調整的隨機波動，增加真實感

### 3. 可配置參數
- 數值變化範圍：設定最小值和最大值
- 變化速度：控制數值變化的快慢
- 波動幅度：調整隨機跳動的程度
- 更新頻率：控制數值更新的間隔

## 配置說明

### 全域設定

```yaml
demo_mode:
  enabled: true                     # 啟用Demo模式
```

### 各指標獨立設定

```yaml
demo_mode:
  indicators:
    # 關節角度Demo設定
    angle:
      enabled: true                 # 啟用角度指標Demo模式
      value_range: [60, 95]         # 數值範圍：60-95分
      change_speed: 2.0             # 變化速度：每秒最大變化2分
      fluctuation: 5                # 波動幅度：±5分的隨機變化
      
    # 行走速度Demo設定  
    speed:
      enabled: true                 # 啟用速度指標Demo模式
      value_range: [70, 90]         # 數值範圍：70-90分
      change_speed: 3.0             # 變化速度：每秒最大變化3分
      fluctuation: 8                # 波動幅度：±8分的隨機變化
```

### 全域參數設定

```yaml
demo_mode:
  global_settings:
    update_interval: 0.1            # 更新間隔：0.1秒
    smooth_transition: true         # 平滑過渡：啟用
    realistic_patterns: true        # 真實模式：啟用週期性變化
    cycle_duration: 10.0            # 週期時間：10秒一個週期
```

## 參數詳解

### value_range (數值範圍)
- **格式**：[最小值, 最大值]
- **範圍**：0-100
- **說明**：Demo數值會在此範圍內變化
- **建議**：
  - 優良表現：[80, 100]
  - 一般表現：[60, 80]
  - 不佳表現：[20, 60]

### change_speed (變化速度)
- **單位**：分/秒
- **範圍**：0.1-10.0
- **說明**：每秒鐘數值變化的最大幅度
- **建議**：
  - 緩慢變化：1.0-2.0
  - 中等變化：2.0-4.0
  - 快速變化：4.0-8.0

### fluctuation (波動幅度)
- **單位**：分
- **範圍**：1-20
- **說明**：隨機波動的幅度，增加真實感
- **建議**：
  - 穩定數據：1-3
  - 一般波動：3-8
  - 高波動：8-15

### update_interval (更新間隔)
- **單位**：秒
- **範圍**：0.05-1.0
- **說明**：數值更新的頻率
- **建議**：
  - 流暢顯示：0.1
  - 節省資源：0.2-0.5

### realistic_patterns (真實模式)
- **類型**：布林值
- **說明**：是否使用週期性變化模式
- **true**：使用正弦波創建週期性變化，更像真實生理數據
- **false**：純隨機變化

### cycle_duration (週期時間)
- **單位**：秒
- **範圍**：5.0-60.0
- **說明**：週期性變化的完整週期時間
- **建議**：
  - 快速演示：5-10秒
  - 正常演示：10-20秒
  - 長期演示：20-60秒

## 使用場景

### 1. 產品展示
```yaml
demo_mode:
  enabled: true
  indicators:
    angle:
      enabled: true
      value_range: [85, 98]         # 展示優良表現
      change_speed: 1.5
      fluctuation: 2
    speed:
      enabled: true
      value_range: [80, 95]
      change_speed: 2.0
      fluctuation: 3
```

### 2. 系統測試
```yaml
demo_mode:
  enabled: true
  indicators:
    angle:
      enabled: true
      value_range: [30, 90]         # 測試全範圍
      change_speed: 5.0             # 快速變化
      fluctuation: 10
```

### 3. 混合模式（部分Demo）
```yaml
demo_mode:
  enabled: true
  indicators:
    angle:
      enabled: false                # 使用真實演算法
    speed:
      enabled: true                 # 使用Demo模式
      value_range: [70, 85]
```

## 注意事項

### 1. 性能考量
- Demo模式會增加少量CPU使用率
- 建議在展示完成後關閉Demo模式

### 2. 數據真實性
- Demo模式僅用於展示，不代表真實的檢測結果
- 在正式使用時請關閉Demo模式

### 3. 配置檢查
- 確保value_range的最小值小於最大值
- change_speed不宜設置過大，避免數值跳動過於劇烈
- update_interval不宜設置過小，避免過度消耗資源

## 故障排除

### Q: Demo模式沒有生效？
A: 檢查以下設定：
1. `demo_mode.enabled` 是否為 `true`
2. 對應指標的 `enabled` 是否為 `true`
3. YAML格式是否正確

### Q: 數值變化太快或太慢？
A: 調整以下參數：
- `change_speed`：控制變化速度
- `update_interval`：控制更新頻率
- `fluctuation`：控制波動幅度

### Q: 想要更真實的變化模式？
A: 設定：
- `realistic_patterns: true`
- 適當的 `cycle_duration`
- 較小的 `fluctuation` 值

## 快速設定範例

### 展示模式（優良表現）
```yaml
demo_mode:
  enabled: true
  indicators:
    angle: { enabled: true, value_range: [85, 98], change_speed: 1.5, fluctuation: 2 }
    speed: { enabled: true, value_range: [80, 95], change_speed: 2.0, fluctuation: 3 }
    shoulder_balance: { enabled: true, value_range: [90, 100], change_speed: 1.0, fluctuation: 1 }
    hunchback: { enabled: true, value_range: [85, 95], change_speed: 1.5, fluctuation: 2 }
  global_settings:
    update_interval: 0.1
    realistic_patterns: true
    cycle_duration: 15.0
```

### 測試模式（全範圍變化）
```yaml
demo_mode:
  enabled: true
  indicators:
    angle: { enabled: true, value_range: [20, 100], change_speed: 4.0, fluctuation: 8 }
    speed: { enabled: true, value_range: [10, 100], change_speed: 5.0, fluctuation: 10 }
    shoulder_balance: { enabled: true, value_range: [20, 100], change_speed: 3.0, fluctuation: 6 }
    hunchback: { enabled: true, value_range: [20, 100], change_speed: 4.0, fluctuation: 8 }
  global_settings:
    update_interval: 0.1
    realistic_patterns: false
    smooth_transition: true
```
